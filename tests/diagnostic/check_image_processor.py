#!/usr/bin/env python
"""
检查ImagePreprocessor类实现的脚本
"""
import os
import sys
import logging
import inspect

# 配置日志
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, root_dir)
logger.info(f"项目根目录: {root_dir}")

# 确保使用config_fake.yaml作为配置文件
os.environ['CONFIG_FILE'] = 'config.yaml'
logger.info(f"使用配置文件: {os.environ['CONFIG_FILE']}")

def check_image_processor_class():
    """检查ImagePreprocessor类的实现"""
    logger.info("检查ImagePreprocessor类的实现...")
    
    try:
        # 导入ImagePreprocessor类
        from app.processor.image_processor import ImagePreprocessor
        
        # 获取类的所有方法
        methods = inspect.getmembers(ImagePreprocessor, predicate=inspect.isfunction)
        logger.info(f"ImagePreprocessor类的方法: {[name for name, _ in methods]}")
        
        # 获取_super_resolution方法的源代码
        super_resolution_method = getattr(ImagePreprocessor, '_super_resolution', None)
        if super_resolution_method:
            source = inspect.getsource(super_resolution_method)
            logger.info(f"_super_resolution方法的源代码:\n{source}")
        else:
            logger.error("未找到_super_resolution方法")
        
        # 检查类的初始化方法
        init_method = getattr(ImagePreprocessor, '__init__', None)
        if init_method:
            source = inspect.getsource(init_method)
            logger.info(f"__init__方法的源代码:\n{source}")
        else:
            logger.error("未找到__init__方法")
        
        # 检查类的属性
        logger.info("尝试创建ImagePreprocessor实例...")
        from unittest.mock import patch
        with patch('app.processor.image_processor.RealESRGANer'), \
             patch('app.processor.image_processor.RRDBNet'):
            processor = ImagePreprocessor()
            logger.info(f"ImagePreprocessor实例的属性: {dir(processor)}")
            
            # 检查关键属性
            logger.info(f"target_size: {processor.target_size}")
            logger.info(f"preprocess: {processor.preprocess}")
            
            # 检查upsampler属性
            if hasattr(processor, 'upsampler'):
                logger.info("实例有upsampler属性")
            else:
                logger.error("实例没有upsampler属性")
            
            # 检查sr_model属性
            if hasattr(processor, 'sr_model'):
                logger.info("实例有sr_model属性")
            else:
                logger.error("实例没有sr_model属性")
            
            # 检查_initialize_sr_model方法
            initialize_sr_method = getattr(processor, '_initialize_sr_model', None)
            if initialize_sr_method:
                logger.info("实例有_initialize_sr_model方法")
            else:
                logger.error("实例没有_initialize_sr_model方法")
    
    except Exception as e:
        logger.error(f"检查ImagePreprocessor类失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    logger.info("开始检查ImagePreprocessor类...")
    check_image_processor_class()
    logger.info("检查完成") 