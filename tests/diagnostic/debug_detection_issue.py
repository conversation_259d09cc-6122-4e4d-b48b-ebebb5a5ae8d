#!/usr/bin/env python3
"""
诊断检测问题的脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import asyncio
import time
import logging
from app.utils.config import config
from app.utils.logger import setup_logger
from app.processor.stream_processor import StreamMonitor
from app.video.manager import VideoStreamManager
from app.utils.shared_state import shared_state_manager

# 设置日志
logger = setup_logger(__name__, 
    console_level=logging.INFO,
    file_level=logging.DEBUG
)

async def test_stream_processor_creation():
    """测试流处理器创建"""
    print("=== 测试流处理器创建 ===")
    
    try:
        # 初始化视频管理器
        video_manager = VideoStreamManager()
        
        # 初始化流监控器
        stream_monitor = StreamMonitor(video_manager)
        
        # 获取活跃无人机列表
        active_drones = shared_state_manager.get_active_drone_ids()
        print(f"活跃无人机列表: {active_drones}")
        
        if not active_drones:
            print("没有活跃的无人机，无法测试")
            return
            
        drone_code = active_drones[0]
        print(f"测试无人机: {drone_code}")
        
        # 尝试获取视频流
        stream = await video_manager.get_stream(drone_code, "drone")
        print(f"获取到视频流: {stream is not None}")
        
        if stream:
            print(f"视频流类型: {type(stream)}")
            print(f"视频流方法: {[method for method in dir(stream) if not method.startswith('_')]}")
            
            # 测试帧获取
            if hasattr(stream, 'get_frame_blocking'):
                print("流支持 get_frame_blocking 方法")
                try:
                    frame = stream.get_frame_blocking(timeout=1.0)
                    print(f"获取帧结果: {frame is not None}")
                except Exception as e:
                    print(f"获取帧失败: {e}")
            else:
                print("流不支持 get_frame_blocking 方法")
                try:
                    frame = await stream.get_frame()
                    print(f"获取帧结果: {frame is not None}")
                except Exception as e:
                    print(f"获取帧失败: {e}")
        
        # 测试检测配置
        print(f"\n=== 检测配置 ===")
        print(f"检测间隔: {config.video_stream.detection_interval}秒")
        print(f"目标ID列表: {config.detection.target_ids}")
        print(f"目标配置数量: {len(config.detection.targets)}")
        
        target_configs = [
            next((t for t in config.detection.targets if t['id'] == int(target_id)), None)
            for target_id in config.detection.target_ids
        ]
        target_configs = [t for t in target_configs if t is not None]
        print(f"有效目标配置数量: {len(target_configs)}")
        
        # 清理
        await video_manager.cleanup()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_stream_processor_creation()

if __name__ == "__main__":
    asyncio.run(main())
