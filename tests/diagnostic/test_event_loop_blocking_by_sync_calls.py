import asyncio
import time
import pytest
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.date import DateTrigger
from datetime import datetime, timedelta


@pytest.mark.asyncio
async def test_blocking_sync_call_inside_async_job_will_stall_event_loop():
    """验证：在异步任务中调用同步阻塞(time.sleep)会阻塞事件循环，导致其他协程显著延迟。
    目的：为定位生产问题提供佐证 —— 如果 APScheduler 任务内部调用了阻塞式同步代码，会出现与现场类似的“日志停滞 + Ctrl+C 无响应”现象。
    """
    loop = asyncio.get_running_loop()

    # 标记器：期望很快完成的协程
    async def quick_task():
        await asyncio.sleep(0.05)
        return "ok"

    # 故意错误：在 async 函数里用 time.sleep 模拟阻塞（代表调用 subprocess.run/系统命令等）
    async def blocking_job():
        time.sleep(1.5)  # 阻塞事件循环 1.5 秒

    scheduler = AsyncIOScheduler(event_loop=loop)
    scheduler.start()
    try:
        # 让阻塞任务尽快执行
        scheduler.add_job(blocking_job, DateTrigger(run_date=datetime.now() + timedelta(milliseconds=50)))

        t0 = time.time()
        # 与阻塞任务并发执行
        res = await quick_task()
        elapsed = time.time() - t0

        # 如果事件循环未被阻塞，quick_task 应 ~0.05s 完成；被阻塞则会接近 1.5s
        assert res == "ok"
        assert elapsed < 0.3, f"事件循环疑似被阻塞: elapsed={elapsed:.3f}s"
    finally:
        scheduler.shutdown(wait=False)


@pytest.mark.asyncio
async def test_monitor_nfs_job_blocking_effect(monkeypatch):
    """验证：模拟 NFS 自检与重挂载任务内部的阻塞，观察事件循环是否被拖住。
    方法：monkeypatch 一个阻塞的 check_and_remount，实现与生产代码路径贴近的复现。
    说明：本测试不依赖实际 sudo/systemctl/mount，仅用 sleep 代替阻塞外部命令。
    """
    from app.services.monitor_service import MonitorService
    from app.utils import nfs_manager as nfs_mod

    loop = asyncio.get_running_loop()

    # 模拟阻塞的 NFS 操作：sleep 1.2s 代表 sudo/systemctl/mount 等外部命令长时间阻塞
    def fake_check_and_remount():
        time.sleep(1.2)
        return False

    monkeypatch.setattr(nfs_mod.nfs_manager, "check_and_remount", fake_check_and_remount, raising=True)

    svc = MonitorService()

    # 用一个快速任务作为“心跳”观测事件循环是否顺畅
    async def heartbeat():
        await asyncio.sleep(0.05)
        return "beat"

    t0 = time.time()
    # 并发触发：1) 心跳  2) 直接调用待测协程（内部会触发阻塞的 check_and_remount）
    hb, _ = await asyncio.gather(
        heartbeat(),
        svc._check_nfs_and_remount(),  # 注意：这是被调度器调用的同一路径
    )
    elapsed = time.time() - t0

    # 期望：若内部使用了同步阻塞，elapsed 将接近 1.2s，从而证明该路径会拖住事件循环
    assert hb == "beat"
    assert elapsed < 0.3, f"NFS 自检协程内部疑似同步阻塞导致事件循环停滞: elapsed={elapsed:.3f}s"

