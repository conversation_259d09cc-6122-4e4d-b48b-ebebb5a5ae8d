#!/usr/bin/env python3
"""
事件循环调试脚本
用于诊断事件循环相关问题
"""
import asyncio
import logging
import threading
import time
import sys
from app.utils.async_frame_queue import AsyncFrameQueue, FrameData
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EventLoopTest:
    """事件循环测试类"""
    
    def __init__(self):
        self.frame_queue = None
        self.loop = None
        self.running = False
        self.frame_count = 0
    
    async def setup_queue(self):
        """设置队列"""
        self.frame_queue = AsyncFrameQueue(max_size=10)
        logger.info("异步帧队列创建成功")
    
    def producer_thread(self):
        """生产者线程"""
        logger.info("生产者线程启动")
        
        while self.running:
            try:
                # 创建测试帧
                frame = np.zeros((100, 100, 3), dtype=np.uint8)
                frame_data = FrameData(
                    frame=frame,
                    timestamp=time.time() * 1000,
                    frame_id=self.frame_count,
                    metadata={"test": True, "source": "debug_test"}
                )
                
                # 尝试入队
                if self.loop and not self.loop.is_closed():
                    try:
                        future = asyncio.run_coroutine_threadsafe(
                            self.frame_queue.put_frame(frame_data),
                            self.loop
                        )
                        result = future.result(timeout=1.0)
                        if result:
                            self.frame_count += 1
                            logger.info(f"成功入队帧 {self.frame_count}")
                        else:
                            logger.warning(f"入队失败: {frame_data.frame_id}")
                    except asyncio.TimeoutError:
                        logger.warning("入队超时")
                    except Exception as e:
                        logger.error(f"入队异常: {str(e)}", exc_info=True)
                        break
                else:
                    logger.error("事件循环不可用")
                    break
                
                time.sleep(1)  # 每秒一帧
                
            except Exception as e:
                logger.error(f"生产者线程异常: {str(e)}", exc_info=True)
                break
        
        logger.info("生产者线程结束")
    
    async def consumer_coroutine(self):
        """消费者协程"""
        logger.info("消费者协程启动")
        consumed_count = 0
        
        while self.running:
            try:
                frame_data = await asyncio.wait_for(
                    self.frame_queue.get_frame(),
                    timeout=2.0
                )
                
                if frame_data:
                    consumed_count += 1
                    logger.info(f"消费帧 {consumed_count}: {frame_data.frame_id}")
                else:
                    logger.debug("获取到空帧")
                
            except asyncio.TimeoutError:
                logger.debug("消费者超时")
                continue
            except Exception as e:
                logger.error(f"消费者异常: {str(e)}", exc_info=True)
                break
        
        logger.info(f"消费者协程结束，总消费: {consumed_count}")
    
    async def run_test(self, duration_seconds=30):
        """运行测试"""
        logger.info(f"开始事件循环测试，时长: {duration_seconds}秒")
        
        # 获取当前事件循环
        self.loop = asyncio.get_running_loop()
        logger.info(f"当前事件循环: {self.loop}")
        
        # 设置队列
        await self.setup_queue()
        
        # 启动标志
        self.running = True
        
        try:
            # 启动生产者线程
            producer_thread = threading.Thread(target=self.producer_thread)
            producer_thread.start()
            logger.info("生产者线程已启动")
            
            # 启动消费者协程
            consumer_task = asyncio.create_task(self.consumer_coroutine())
            logger.info("消费者协程已启动")
            
            # 等待指定时间
            await asyncio.sleep(duration_seconds)
            
            logger.info("测试时间结束，正在停止...")
            
        except Exception as e:
            logger.error(f"测试过程中发生异常: {str(e)}", exc_info=True)
        
        finally:
            # 停止所有组件
            self.running = False
            
            # 等待生产者线程结束
            if 'producer_thread' in locals():
                producer_thread.join(timeout=5)
                if producer_thread.is_alive():
                    logger.warning("生产者线程未能正常结束")
                else:
                    logger.info("生产者线程已结束")
            
            # 取消消费者任务
            if 'consumer_task' in locals():
                consumer_task.cancel()
                try:
                    await consumer_task
                except asyncio.CancelledError:
                    logger.info("消费者协程已取消")
            
            # 关闭队列
            if self.frame_queue:
                await self.frame_queue.close()
                logger.info("帧队列已关闭")
            
            # 输出最终统计
            logger.info(f"测试完成，总生产帧数: {self.frame_count}")

async def main():
    """主函数"""
    duration = 30
    if len(sys.argv) > 1:
        try:
            duration = int(sys.argv[1])
        except ValueError:
            logger.error("请提供有效的测试时长（秒）")
            sys.exit(1)
    
    test = EventLoopTest()
    await test.run_test(duration)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)
