#!/usr/bin/env python
"""
调试测试的脚本，提供更详细的信息
"""
import os
import sys
import pytest
import logging
import importlib
import inspect

# 配置日志
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, root_dir)
logger.info(f"项目根目录: {root_dir}")

# 确保使用config_fake.yaml作为配置文件
os.environ['CONFIG_FILE'] = 'config.yaml'
logger.info(f"使用配置文件: {os.environ['CONFIG_FILE']}")

def check_imports():
    """检查关键模块是否可以正确导入"""
    logger.info("检查关键模块导入...")
    
    try:
        import app
        logger.info(f"成功导入 app 模块，路径: {app.__file__}")
    except ImportError as e:
        logger.error(f"导入 app 模块失败: {e}")
    
    try:
        from app.utils import event_reporter
        logger.info(f"成功导入 event_reporter 模块，路径: {event_reporter.__file__}")
    except ImportError as e:
        logger.error(f"导入 event_reporter 模块失败: {e}")
    
    try:
        from app.processor import image_processor
        logger.info(f"成功导入 image_processor 模块，路径: {image_processor.__file__}")
    except ImportError as e:
        logger.error(f"导入 image_processor 模块失败: {e}")

def check_test_files():
    """检查测试文件是否存在并可以导入"""
    logger.info("检查测试文件...")
    
    test_dir = os.path.dirname(os.path.abspath(__file__))
    unit_dir = os.path.join(test_dir, 'unit')
    
    if not os.path.exists(unit_dir):
        logger.error(f"单元测试目录不存在: {unit_dir}")
        return
    
    logger.info(f"单元测试目录存在: {unit_dir}")
    
    # 检查初始化测试
    init_test_path = os.path.join(unit_dir, 'test_init.py')
    if os.path.exists(init_test_path):
        logger.info(f"初始化测试文件存在: {init_test_path}")
        try:
            spec = importlib.util.spec_from_file_location("test_init", init_test_path)
            test_init = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(test_init)
            logger.info("成功导入初始化测试模块")
            
            # 查找测试函数
            test_functions = [name for name, obj in inspect.getmembers(test_init) 
                             if name.startswith('test_') and callable(obj)]
            logger.info(f"找到测试函数: {test_functions}")
        except Exception as e:
            logger.error(f"导入初始化测试模块失败: {e}")
    else:
        logger.error(f"初始化测试文件不存在: {init_test_path}")
    
    # 检查 event_reporter 测试
    er_test_path = os.path.join(unit_dir, 'app', 'utils', 'test_event_reporter.py')
    if os.path.exists(er_test_path):
        logger.info(f"EventReporter测试文件存在: {er_test_path}")
    else:
        logger.error(f"EventReporter测试文件不存在: {er_test_path}")
    
    # 检查 image_processor 测试
    ip_test_path = os.path.join(unit_dir, 'app', 'processor', 'test_image_processor.py')
    if os.path.exists(ip_test_path):
        logger.info(f"ImageProcessor测试文件存在: {ip_test_path}")
    else:
        logger.error(f"ImageProcessor测试文件不存在: {ip_test_path}")

if __name__ == "__main__":
    # 检查导入
    check_imports()
    
    # 检查测试文件
    check_test_files()
    
    # 获取测试目录的完整路径
    test_dir = os.path.dirname(os.path.abspath(__file__))
    unit_dir = os.path.join(test_dir, 'unit')
    
    logger.info("测试目录内容:")
    for root, dirs, files in os.walk(test_dir):
        rel_path = os.path.relpath(root, test_dir)
        if rel_path == '.':
            logger.info(f"根目录: {files}")
        else:
            logger.info(f"子目录 {rel_path}: {files}")
    
    # 运行测试
    logger.info("开始运行测试...")
    result = pytest.main(["-xvs", unit_dir]) 