#!/usr/bin/env python3
"""
简单的RTMP流测试脚本
用于验证事件循环修复
"""
import asyncio
import logging
import time
import sys
from app.video.streams.rtmp import RTMPStream

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_rtmp_stream(stream_id: str, duration_seconds: int = 30):
    """测试RTMP流"""
    logger.info(f"开始测试RTMP流，流ID: {stream_id}")
    
    stream = None
    try:
        # 创建RTMP流
        stream = RTMPStream()
        logger.info("创建RTMP流实例成功")
        
        # 启动流
        success = await stream.start(stream_id)
        if not success:
            logger.error("启动RTMP流失败")
            return
        
        logger.info("RTMP流启动成功，开始获取帧...")
        
        start_time = time.time()
        frame_count = 0
        last_stats_time = start_time
        
        while time.time() - start_time < duration_seconds:
            try:
                # 获取帧
                frame = await asyncio.wait_for(stream.get_frame(), timeout=1.0)
                
                if frame is not None:
                    frame_count += 1
                    if frame_count % 10 == 0:
                        logger.info(f"已获取 {frame_count} 帧")
                
                # 每10秒输出统计
                current_time = time.time()
                if current_time - last_stats_time >= 10:
                    elapsed = current_time - start_time
                    fps = frame_count / elapsed if elapsed > 0 else 0
                    logger.info(f"运行统计: 时长={elapsed:.1f}s, 帧数={frame_count}, FPS={fps:.2f}")
                    
                    # 获取生产者统计
                    if hasattr(stream, 'frame_producer') and stream.frame_producer:
                        producer_stats = stream.frame_producer.get_stats()
                        logger.info(f"生产者统计: {producer_stats}")
                    
                    last_stats_time = current_time
                
            except asyncio.TimeoutError:
                logger.debug("获取帧超时")
                continue
            except Exception as e:
                logger.error(f"获取帧时发生异常: {str(e)}")
                break
        
        # 输出最终统计
        total_time = time.time() - start_time
        final_fps = frame_count / total_time if total_time > 0 else 0
        logger.info(f"测试完成: 总时长={total_time:.1f}s, 总帧数={frame_count}, 平均FPS={final_fps:.2f}")
        
        # 获取最终生产者统计
        if hasattr(stream, 'frame_producer') and stream.frame_producer:
            final_stats = stream.frame_producer.get_stats()
            logger.info(f"最终生产者统计: {final_stats}")
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {str(e)}", exc_info=True)
    
    finally:
        # 停止流
        if stream:
            try:
                logger.info("正在停止RTMP流...")
                await stream.stop()
                logger.info("RTMP流已停止")
            except Exception as e:
                logger.error(f"停止RTMP流时发生异常: {str(e)}")

async def main():
    """主函数"""
    if len(sys.argv) < 2:
        logger.error("请提供流ID")
        logger.info("用法: python test_rtmp_stream_simple.py <stream_id> [duration_seconds]")
        sys.exit(1)
    
    stream_id = sys.argv[1]
    
    if len(sys.argv) > 2:
        try:
            duration = int(sys.argv[2])
        except ValueError:
            logger.error("请提供有效的测试时长（秒）")
            sys.exit(1)
    else:
        duration = 30  # 默认30秒
    
    await test_rtmp_stream(stream_id, duration)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)
