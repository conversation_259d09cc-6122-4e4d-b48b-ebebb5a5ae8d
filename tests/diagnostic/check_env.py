#!/usr/bin/env python
"""
检查测试环境的脚本
"""
import os
import sys
import logging
import importlib
import platform

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    logger.info(f"Python版本: {platform.python_version()}")
    logger.info(f"Python路径: {sys.executable}")
    logger.info(f"平台信息: {platform.platform()}")

def check_pytest():
    """检查pytest是否安装"""
    try:
        import pytest
        logger.info(f"pytest版本: {pytest.__version__}")
    except ImportError:
        logger.error("pytest未安装")

def check_pytest_asyncio():
    """检查pytest-asyncio是否安装"""
    try:
        import pytest_asyncio
        logger.info(f"pytest-asyncio版本: {pytest_asyncio.__version__}")
    except ImportError:
        logger.error("pytest-asyncio未安装")

def check_numpy():
    """检查numpy是否安装"""
    try:
        import numpy
        logger.info(f"numpy版本: {numpy.__version__}")
    except ImportError:
        logger.error("numpy未安装")

def check_opencv():
    """检查opencv是否安装"""
    try:
        import cv2
        logger.info(f"OpenCV版本: {cv2.__version__}")
    except ImportError:
        logger.error("OpenCV未安装")

def check_paths():
    """检查路径设置"""
    # 获取当前脚本路径
    current_path = os.path.abspath(__file__)
    logger.info(f"当前脚本路径: {current_path}")
    
    # 获取测试目录路径
    test_dir = os.path.dirname(current_path)
    logger.info(f"测试目录路径: {test_dir}")
    
    # 获取项目根目录路径
    root_dir = os.path.abspath(os.path.join(test_dir, '..'))
    logger.info(f"项目根目录路径: {root_dir}")
    
    # 检查Python路径
    logger.info("Python路径:")
    for path in sys.path:
        logger.info(f"  {path}")
    
    # 检查测试目录结构
    logger.info("测试目录结构:")
    for root, dirs, files in os.walk(test_dir):
        rel_path = os.path.relpath(root, test_dir)
        if rel_path == '.':
            logger.info(f"  根目录: {files}")
        else:
            logger.info(f"  子目录 {rel_path}: {files}")

if __name__ == "__main__":
    logger.info("开始检查测试环境...")
    
    check_python_version()
    check_pytest()
    check_pytest_asyncio()
    check_numpy()
    check_opencv()
    check_paths()
    
    logger.info("测试环境检查完成") 