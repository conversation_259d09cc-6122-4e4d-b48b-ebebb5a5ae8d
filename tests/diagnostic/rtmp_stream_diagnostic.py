#!/usr/bin/env python3
"""
RTMP流诊断脚本
用于直接测试RTMP流是否有数据
"""
import cv2
import time
import logging
import sys
import signal
from typing import Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RTMPStreamDiagnostic:
    """RTMP流诊断类"""
    
    def __init__(self, rtmp_url: str):
        self.rtmp_url = rtmp_url
        self.running = True
        self.cap: Optional[cv2.VideoCapture] = None
        self.stats = {
            'start_time': None,
            'total_attempts': 0,
            'successful_reads': 0,
            'empty_frames': 0,
            'read_failures': 0,
            'connection_attempts': 0,
            'connection_failures': 0
        }
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，正在停止诊断...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def connect(self) -> bool:
        """连接到RTMP流"""
        self.stats['connection_attempts'] += 1
        logger.info(f"尝试连接到RTMP流: {self.rtmp_url}")
        
        try:
            # 释放现有连接
            if self.cap:
                self.cap.release()
                self.cap = None
            
            # 创建新连接
            self.cap = cv2.VideoCapture(self.rtmp_url, cv2.CAP_FFMPEG)
            
            # 设置超时参数
            self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 10000)  # 10秒连接超时
            self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)   # 5秒读取超时
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)             # 最小缓冲区
            
            if not self.cap.isOpened():
                logger.error("VideoCapture无法打开流")
                self.stats['connection_failures'] += 1
                return False
            
            # 获取流信息
            fps = self.cap.get(cv2.CAP_PROP_FPS)
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            logger.info(f"连接成功！流信息: {width}x{height}, FPS: {fps}")
            return True
            
        except Exception as e:
            logger.error(f"连接失败: {str(e)}")
            self.stats['connection_failures'] += 1
            return False
    
    def read_frame(self) -> tuple[bool, Optional[any]]:
        """读取一帧"""
        if not self.cap or not self.cap.isOpened():
            return False, None
        
        try:
            ret, frame = self.cap.read()
            return ret, frame
        except Exception as e:
            logger.error(f"读取帧时发生异常: {str(e)}")
            return False, None
    
    def print_stats(self):
        """打印统计信息"""
        if self.stats['start_time']:
            duration = time.time() - self.stats['start_time']
            logger.info("=== RTMP流诊断统计 ===")
            logger.info(f"诊断时长: {duration:.1f} 秒")
            logger.info(f"连接尝试: {self.stats['connection_attempts']}")
            logger.info(f"连接失败: {self.stats['connection_failures']}")
            logger.info(f"总读取尝试: {self.stats['total_attempts']}")
            logger.info(f"成功读取: {self.stats['successful_reads']}")
            logger.info(f"空帧: {self.stats['empty_frames']}")
            logger.info(f"读取失败: {self.stats['read_failures']}")
            
            if duration > 0:
                fps = self.stats['successful_reads'] / duration
                success_rate = (self.stats['successful_reads'] / self.stats['total_attempts'] * 100) if self.stats['total_attempts'] > 0 else 0
                logger.info(f"平均FPS: {fps:.2f}")
                logger.info(f"成功率: {success_rate:.1f}%")
    
    def run_diagnostic(self, duration_seconds=30):
        """运行诊断"""
        logger.info(f"开始RTMP流诊断，URL: {self.rtmp_url}")
        logger.info(f"诊断时长: {duration_seconds} 秒")
        
        # 设置信号处理器
        self.setup_signal_handlers()
        
        # 记录开始时间
        self.stats['start_time'] = time.time()
        
        try:
            # 尝试连接
            if not self.connect():
                logger.error("无法连接到流，诊断结束")
                return
            
            end_time = time.time() + duration_seconds
            last_stats_time = time.time()
            
            while self.running and time.time() < end_time:
                # 如果连接断开，尝试重连
                if not self.cap or not self.cap.isOpened():
                    logger.warning("连接断开，尝试重连...")
                    if not self.connect():
                        logger.error("重连失败，等待5秒后重试")
                        time.sleep(5)
                        continue
                
                # 读取帧
                self.stats['total_attempts'] += 1
                ret, frame = self.read_frame()
                
                if ret and frame is not None and frame.size > 0:
                    self.stats['successful_reads'] += 1
                    # 每100个成功帧记录一次
                    if self.stats['successful_reads'] % 100 == 0:
                        logger.info(f"已成功读取 {self.stats['successful_reads']} 帧")
                elif ret and (frame is None or frame.size == 0):
                    self.stats['empty_frames'] += 1
                else:
                    self.stats['read_failures'] += 1
                
                # 每10秒输出一次统计
                current_time = time.time()
                if current_time - last_stats_time >= 10:
                    duration = current_time - self.stats['start_time']
                    fps = self.stats['successful_reads'] / duration if duration > 0 else 0
                    logger.info(f"中间统计 - 成功: {self.stats['successful_reads']}, "
                              f"空帧: {self.stats['empty_frames']}, "
                              f"失败: {self.stats['read_failures']}, "
                              f"FPS: {fps:.2f}")
                    last_stats_time = current_time
                
                # 短暂休眠避免过度占用CPU
                time.sleep(0.01)
            
            logger.info("诊断时间结束或收到停止信号")
            
        except Exception as e:
            logger.error(f"诊断过程中发生异常: {str(e)}")
        
        finally:
            # 清理资源
            if self.cap:
                self.cap.release()
                self.cap = None
            
            # 打印最终统计
            self.print_stats()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        logger.error("请提供RTMP URL")
        logger.info("用法: python rtmp_stream_diagnostic.py <rtmp_url> [duration_seconds]")
        sys.exit(1)
    
    rtmp_url = sys.argv[1]
    
    if len(sys.argv) > 2:
        try:
            duration = int(sys.argv[2])
        except ValueError:
            logger.error("请提供有效的诊断时长（秒）")
            sys.exit(1)
    else:
        duration = 30  # 默认30秒
    
    diagnostic = RTMPStreamDiagnostic(rtmp_url)
    diagnostic.run_diagnostic(duration)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("诊断被用户中断")
    except Exception as e:
        logger.error(f"诊断失败: {str(e)}")
        sys.exit(1)
