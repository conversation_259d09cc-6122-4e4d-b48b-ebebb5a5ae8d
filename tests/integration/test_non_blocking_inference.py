#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试：确保将推理放入线程池后不会阻塞事件循环，且功能不受影响。
- 使用 DummyModel 模拟耗时推理，验证 detect_targets 不阻塞事件循环（通过并发任务完成时间判断）
- 验证返回值逻辑保持不变（无检测/有检测的路径）

注意：
- 放在 tests/ 目录下，不自动运行，等待手工执行
- 测试使用 DummyImagePreprocessor 避免加载真实模型
"""
import sys
import os
import asyncio
import time
import numpy as np
import pytest

# 添加项目根目录到Python路径
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
root_dir = os.path.abspath(os.path.join(root_dir, '..'))
sys.path.insert(0, root_dir)

from app.processor.image_processor import ImagePreprocessor


class DummyModel:
    """模拟一个耗时的检测模型。"""
    def __init__(self, delay_s: float = 0.2, detections: bool = False):
        self.delay_s = delay_s
        self._detections = detections
    
    def detect_multiple(self, image: np.ndarray, targets):
        # 模拟CPU耗时
        t0 = time.time()
        # 用 busy wait 模拟CPU占用，避免真正sleep释放GIL造成误判
        while time.time() - t0 < self.delay_s:
            pass
        if self._detections:
            return {"detections": [{"bbox": [1, 2, 3, 4], "confidence": 0.9, "label": "dummy"}]}
        return None


class DummyImagePreprocessor(ImagePreprocessor):
    def __init__(self):
        super().__init__(model_type="cv")
        # 覆盖真实模型，使用假模型
        self.model = DummyModel(delay_s=0.2, detections=False)
        self.preprocess = False
    

@pytest.mark.asyncio
async def test_inference_runs_in_threadpool_non_blocking():
    """验证推理在线程池执行，不阻塞事件循环。"""
    proc = DummyImagePreprocessor()
    img = np.zeros((100, 100, 3), dtype=np.uint8)
    targets = [{"id": 1, "name": "t1", "keyword": "t1"}]

    async def concurrent_task():
        # 一个并发协程，若事件循环被阻塞，完成时间将显著延迟
        await asyncio.sleep(0.05)
        return "ok"

    # 并发执行：检测+一个短sleep任务
    t0 = time.time()
    res, other = await asyncio.gather(
        proc.detect_targets(img, targets),
        concurrent_task(),
    )
    elapsed = time.time() - t0

    # 断言：concurrent_task 能按期完成（总耗时接近模型耗时，但sleep任务未被阻塞到>0.5s）
    assert elapsed < 0.5, f"事件循环疑似被阻塞, elapsed={elapsed:.3f}s"
    assert res is None, "无检测时应返回 None"
    assert other == "ok"


@pytest.mark.asyncio
async def test_inference_result_consistency_when_has_detections():
    """验证有检测结果时的返回值结构不变。"""
    proc = DummyImagePreprocessor()
    proc.model = DummyModel(delay_s=0.05, detections=True)
    proc.preprocess = False
    img = np.zeros((64, 64, 3), dtype=np.uint8)
    targets = [{"id": 1, "name": "t1", "keyword": "t1"}]

    res = await proc.detect_targets(img, targets, drone_code="D1")
    assert isinstance(res, dict)
    assert "detections" in res
    assert isinstance(res.get("detections"), list)
    # 不要求一定有 image_paths（需触发保存），这里只校验基本结构

