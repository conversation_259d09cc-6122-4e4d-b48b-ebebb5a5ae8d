#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试：验证帧桥接与检测触发链路
- 构造一个 RTMPStream 实例，模拟向 AsyncFrameQueue 放入帧
- 启动桥接协程，将帧转移到 ThreadFrameQueue
- 验证 get_frame_blocking 能够取到非 None 帧
- 模拟 StreamProcessor 的检测触发，spy detect_targets 是否被调用

注意：
- 本测试不依赖真实 RTMP 源，不会对外发起网络连接
- 不运行，由使用者手动执行
"""
import asyncio
import numpy as np
import time
import types
import pytest
import sys
import os

# 添加项目根目录到Python路径
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, root_dir)

from app.video.streams.rtmp import RTMPStream
from app.utils.async_frame_queue import AsyncFrameQueue
from app.utils.thread_frame_queue import ThreadFrameQueue
from app.utils.frame_data import FrameData
from app.processor.stream_processor import StreamProcessor


@pytest.mark.asyncio
async def test_frame_bridging_and_blocking_get(monkeypatch):
    """验证桥接任务能够把帧从 AsyncFrameQueue 转到 ThreadFrameQueue，并可阻塞读取。"""
    s = RTMPStream()

    # 注入模拟的队列与状态
    s.frame_queue = AsyncFrameQueue(max_size=10, drop_old_frames=True)
    s.thread_frame_queue = ThreadFrameQueue(max_size=10, drop_old_frames=True)
    s.is_running = True
    s.stream_key = "TEST_STREAM_rtmp"

    # 启动桥接任务
    task = asyncio.create_task(s._bridge_frames())

    # 放入三帧
    img = np.zeros((10, 10, 3), dtype=np.uint8)
    for i in range(3):
        await s.frame_queue.put_frame(FrameData(frame=img.copy(), timestamp=time.time()*1000, frame_id=i+1, metadata={}))

    # 等待桥接执行
    await asyncio.sleep(0.1)

    # 从阻塞队列读取
    frame = s.get_frame_blocking(timeout=0.5)
    assert frame is not None, "桥接后应能从阻塞队列取到帧"

    # 清理
    task.cancel()
    try:
        await task
    except Exception:
        pass

@pytest.mark.asyncio
async def test_stream_processor_triggers_detection(monkeypatch):
    """验证 StreamProcessor 能在拿到帧后触发 detect_targets 调用。"""
    # 构造假流对象，提供 get_frame_blocking 返回有效帧
    class DummyStream:
        def __init__(self):
            self.calls = 0
        def get_frame_blocking(self, timeout: float = 1.0):
            self.calls += 1
            img = np.zeros((20, 20, 3), dtype=np.uint8)
            return img
        async def stop(self):
            pass

    # Spy detect_targets
    called = {"n": 0}
    async def fake_detect_targets(image, targets, drone_code=None):
        called["n"] += 1
        # 返回空检测结果也可
        return {"detections": []}

    # 替换 ImagePreprocessor.detect_targets
    from app.processor import image_processor as imgp
    monkeypatch.setattr(imgp.ImagePreprocessor, "detect_targets", fake_detect_targets, raising=True)

    # 构造处理器并启动
    sp = StreamProcessor(drone_code="D1", stream_id="D1_drone", stream_type="drone", on_detection=None)

    # 将检测间隔设为极小，加速触发
    sp.detection_interval = 0.01
    sp.frame_rate = 100

    ok = await sp.start(DummyStream())
    assert ok is True

    # 等待一小段时间让线程循环跑起来并触发检测
    await asyncio.sleep(0.2)

    # 期望 detect_targets 至少被调用一次
    assert called["n"] > 0, "应当触发 detect_targets 调用"

    # 停止处理器
    await sp.stop()
