#!/usr/bin/env python3
"""
测试帧生产者改进效果
"""
import asyncio
import logging
import time
import sys
from app.video.streams.frame_producer import FrameProducer
from app.video.streams.rtmp_connection_manager import RTMPConnectionManager
from app.utils.async_frame_queue import AsyncFrameQueue

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_frame_producer(rtmp_url: str, duration_seconds: int = 60):
    """测试帧生产者"""
    logger.info(f"开始测试帧生产者，URL: {rtmp_url}")
    
    # 创建组件
    connection_manager = RTMPConnectionManager(rtmp_url)
    frame_queue = AsyncFrameQueue(max_size=100)
    
    # 创建帧生产者
    producer = FrameProducer(
        connection_manager=connection_manager,
        frame_queue=frame_queue,
        stream_key="test_producer",
        on_error=lambda e: logger.error(f"生产者错误: {e}")
    )
    
    # 设置事件循环
    producer.set_event_loop(asyncio.get_running_loop())
    
    try:
        # 启动生产者
        if not producer.start_producing():
            logger.error("无法启动帧生产者")
            return
        
        logger.info("帧生产者已启动，开始监控...")
        
        start_time = time.time()
        last_stats_time = start_time
        last_stats = None
        
        while time.time() - start_time < duration_seconds:
            # 获取统计信息
            current_stats = producer.get_stats()
            
            # 每10秒输出统计
            current_time = time.time()
            if current_time - last_stats_time >= 10:
                logger.info("=== 生产者统计 ===")
                logger.info(f"运行状态: {current_stats['is_running']}")
                logger.info(f"总帧数: {current_stats['frame_count']}")
                logger.info(f"成功读取: {current_stats['successful_reads']}")
                logger.info(f"总失败: {current_stats['total_failures']}")
                logger.info(f"总空帧: {current_stats.get('total_empty_frames', 'N/A')}")
                logger.info(f"连续失败: {current_stats['consecutive_failures']}")
                logger.info(f"连续空帧: {current_stats.get('consecutive_empty_frames', 'N/A')}")
                logger.info(f"队列大小: {current_stats['queue_size']}")
                logger.info(f"平均FPS: {current_stats['avg_fps']:.2f}")
                
                # 计算增量
                if last_stats:
                    interval = current_time - last_stats_time
                    frame_delta = current_stats['successful_reads'] - last_stats['successful_reads']
                    failure_delta = current_stats['total_failures'] - last_stats['total_failures']
                    empty_delta = current_stats.get('total_empty_frames', 0) - last_stats.get('total_empty_frames', 0)
                    
                    logger.info(f"过去{interval:.0f}秒增量: 成功帧+{frame_delta}, 失败+{failure_delta}, 空帧+{empty_delta}")
                
                last_stats = current_stats.copy()
                last_stats_time = current_time
            
            # 尝试从队列获取帧
            try:
                frame_data = await asyncio.wait_for(frame_queue.get_frame(), timeout=1.0)
                if frame_data:
                    logger.debug(f"从队列获取到帧: {frame_data.frame_id}")
            except asyncio.TimeoutError:
                logger.debug("队列获取帧超时")
            except Exception as e:
                logger.warning(f"从队列获取帧时出错: {e}")
            
            await asyncio.sleep(1)
        
        logger.info("测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {str(e)}")
    
    finally:
        # 停止生产者
        logger.info("正在停止帧生产者...")
        producer.stop_producing()
        
        # 等待生产者完全停止
        await asyncio.sleep(2)
        
        # 输出最终统计
        final_stats = producer.get_stats()
        logger.info("=== 最终统计 ===")
        logger.info(f"总运行时间: {final_stats['elapsed_time']:.1f} 秒")
        logger.info(f"总帧数: {final_stats['frame_count']}")
        logger.info(f"成功读取: {final_stats['successful_reads']}")
        logger.info(f"总失败: {final_stats['total_failures']}")
        logger.info(f"总空帧: {final_stats.get('total_empty_frames', 'N/A')}")
        logger.info(f"平均FPS: {final_stats['avg_fps']:.2f}")
        
        if final_stats['total_failures'] + final_stats.get('total_empty_frames', 0) > 0:
            total_issues = final_stats['total_failures'] + final_stats.get('total_empty_frames', 0)
            total_attempts = final_stats['successful_reads'] + total_issues
            success_rate = (final_stats['successful_reads'] / total_attempts * 100) if total_attempts > 0 else 0
            logger.info(f"成功率: {success_rate:.1f}%")

async def main():
    """主函数"""
    if len(sys.argv) < 2:
        logger.error("请提供RTMP URL")
        logger.info("用法: python test_frame_producer_improvements.py <rtmp_url> [duration_seconds]")
        sys.exit(1)
    
    rtmp_url = sys.argv[1]
    
    if len(sys.argv) > 2:
        try:
            duration = int(sys.argv[2])
        except ValueError:
            logger.error("请提供有效的测试时长（秒）")
            sys.exit(1)
    else:
        duration = 60  # 默认60秒
    
    await test_frame_producer(rtmp_url, duration)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)
