#!/usr/bin/env python3
"""
验证修复效果的简化测试脚本
"""
import asyncio
import logging
import time
import sys
from app.video.streams.rtmp import RTMPStream

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_basic_functionality(stream_id: str, duration_seconds: int = 60):
    """测试基本功能"""
    logger.info(f"开始基本功能测试，流ID: {stream_id}, 时长: {duration_seconds}秒")
    
    stream = None
    try:
        # 创建并启动流
        stream = RTMPStream()
        success = await stream.start(stream_id)
        
        if not success:
            logger.error("启动RTMP流失败")
            return False
        
        logger.info("RTMP流启动成功")
        
        # 等待一段时间让流稳定
        await asyncio.sleep(5)
        
        # 获取一些帧来验证功能
        frame_count = 0
        start_time = time.time()
        last_log_time = start_time
        
        while time.time() - start_time < duration_seconds:
            try:
                frame = await asyncio.wait_for(stream.get_frame(), timeout=2.0)
                
                if frame is not None:
                    frame_count += 1
                    
                    # 每10帧或每10秒记录一次
                    current_time = time.time()
                    if frame_count % 10 == 0 or current_time - last_log_time >= 10:
                        elapsed = current_time - start_time
                        fps = frame_count / elapsed if elapsed > 0 else 0
                        logger.info(f"获取帧进度: {frame_count} 帧, {elapsed:.1f}秒, FPS: {fps:.2f}")
                        last_log_time = current_time
                
            except asyncio.TimeoutError:
                logger.debug("获取帧超时，继续...")
                continue
            except Exception as e:
                logger.error(f"获取帧时发生异常: {str(e)}")
                break
        
        # 输出最终结果
        total_time = time.time() - start_time
        final_fps = frame_count / total_time if total_time > 0 else 0
        
        logger.info(f"测试完成: 总时长={total_time:.1f}s, 总帧数={frame_count}, 平均FPS={final_fps:.2f}")
        
        # 获取生产者统计
        if hasattr(stream, 'frame_producer') and stream.frame_producer:
            stats = stream.frame_producer.get_stats()
            logger.info(f"生产者统计: 成功读取={stats['successful_reads']}, "
                       f"失败={stats['total_failures']}, "
                       f"空帧={stats.get('total_empty_frames', 'N/A')}, "
                       f"运行状态={stats['is_running']}")
        
        # 判断测试是否成功
        success = frame_count > 0 and final_fps > 0.1
        if success:
            logger.info("✅ 基本功能测试通过")
        else:
            logger.error("❌ 基本功能测试失败")
        
        return success
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {str(e)}", exc_info=True)
        return False
    
    finally:
        # 清理资源
        if stream:
            try:
                logger.info("正在停止RTMP流...")
                await stream.stop()
                logger.info("RTMP流已停止")
            except Exception as e:
                logger.error(f"停止RTMP流时发生异常: {str(e)}")

async def test_restart_resilience(stream_id: str, cycles: int = 3):
    """测试重启恢复能力"""
    logger.info(f"开始重启恢复测试，流ID: {stream_id}, 循环次数: {cycles}")
    
    success_count = 0
    
    for cycle in range(cycles):
        logger.info(f"开始第 {cycle + 1}/{cycles} 次循环")
        
        try:
            # 创建并启动流
            stream = RTMPStream()
            success = await stream.start(stream_id)
            
            if not success:
                logger.error(f"第 {cycle + 1} 次循环启动失败")
                continue
            
            # 运行一小段时间
            frame_count = 0
            start_time = time.time()
            
            while time.time() - start_time < 10:  # 每次运行10秒
                try:
                    frame = await asyncio.wait_for(stream.get_frame(), timeout=1.0)
                    if frame is not None:
                        frame_count += 1
                except asyncio.TimeoutError:
                    continue
                except Exception as e:
                    logger.warning(f"获取帧异常: {str(e)}")
                    break
            
            # 停止流
            await stream.stop()
            
            # 评估这次循环
            if frame_count > 0:
                success_count += 1
                logger.info(f"✅ 第 {cycle + 1} 次循环成功，获取 {frame_count} 帧")
            else:
                logger.error(f"❌ 第 {cycle + 1} 次循环失败，未获取到帧")
            
            # 等待一下再开始下一次循环
            await asyncio.sleep(2)
            
        except Exception as e:
            logger.error(f"第 {cycle + 1} 次循环发生异常: {str(e)}")
    
    # 输出最终结果
    success_rate = (success_count / cycles) * 100
    logger.info(f"重启恢复测试完成: {success_count}/{cycles} 次成功，成功率: {success_rate:.1f}%")
    
    return success_rate >= 80  # 80%以上成功率认为通过

async def main():
    """主函数"""
    if len(sys.argv) < 2:
        logger.error("请提供流ID")
        logger.info("用法: python test_fixes_verification.py <stream_id> [test_type]")
        logger.info("test_type: basic (默认) 或 restart")
        sys.exit(1)
    
    stream_id = sys.argv[1]
    test_type = sys.argv[2] if len(sys.argv) > 2 else "basic"
    
    if test_type == "basic":
        success = await test_basic_functionality(stream_id, 60)
    elif test_type == "restart":
        success = await test_restart_resilience(stream_id, 3)
    else:
        logger.error(f"未知的测试类型: {test_type}")
        sys.exit(1)
    
    if success:
        logger.info("🎉 所有测试通过！")
        sys.exit(0)
    else:
        logger.error("💥 测试失败！")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)
