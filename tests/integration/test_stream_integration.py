import pytest
import asyncio
import numpy as np
import time
from unittest.mock import AsyncMock, MagicMock, patch
import threading

# 添加项目根目录到路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加项目根目录到Python路径
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, root_dir)

from app.utils.async_frame_queue import AsyncFrameQueue
from app.utils.frame_data import FrameData
from app.video.streams.rtmp_connection_manager import RTMPConnectionManager
from app.video.streams.frame_producer import FrameProducer
from app.utils.component_monitor import ComponentMonitor, ComponentState


class TestStreamIntegration:
    """流处理集成测试类"""
    
    @pytest.fixture
    def mock_rtmp_url(self):
        """模拟RTMP URL"""
        return "rtmp://test-server:1935/live/test-stream"
    
    @pytest.fixture
    def frame_queue(self):
        """创建帧队列"""
        return AsyncFrameQueue(max_size=50, drop_old_frames=True)
    
    @pytest.fixture
    def mock_connection_manager(self, mock_rtmp_url):
        """创建模拟连接管理器"""
        manager = RTMPConnectionManager(mock_rtmp_url)
        
        # 模拟VideoCapture
        mock_cap = MagicMock()
        mock_cap.isOpened.return_value = True
        mock_cap.read.return_value = (True, np.zeros((480, 640, 3), dtype=np.uint8))
        
        manager._cap = mock_cap
        manager._is_connected = True
        
        return manager
    
    @pytest.mark.asyncio
    async def test_connection_manager_lifecycle(self, mock_connection_manager):
        """测试连接管理器生命周期"""
        # 测试连接创建
        with patch('cv2.VideoCapture') as mock_cv2:
            mock_cap = MagicMock()
            mock_cap.isOpened.return_value = True
            mock_cap.read.return_value = (True, np.zeros((480, 640, 3), dtype=np.uint8))
            mock_cv2.return_value = mock_cap
            
            success = await mock_connection_manager.create_connection()
            assert success is True
            assert mock_connection_manager.is_connected() is True
        
        # 测试健康检查
        health = await mock_connection_manager.check_health()
        assert health is True
        
        # 测试关闭连接
        await mock_connection_manager.close_connection()
        assert mock_connection_manager.is_connected() is False
    
    @pytest.mark.asyncio
    async def test_frame_producer_lifecycle(self, mock_connection_manager, frame_queue):
        """测试帧生产者生命周期"""
        error_callback = MagicMock()
        
        producer = FrameProducer(
            connection_manager=mock_connection_manager,
            frame_queue=frame_queue,
            stream_key="test_stream",
            on_error=error_callback
        )

        # 设置事件循环
        current_loop = asyncio.get_running_loop()
        producer.set_event_loop(current_loop)

        # 启动生产者
        success = producer.start_producing()
        assert success is True
        assert producer.is_running() is True
        
        # 等待一些帧被生产
        await asyncio.sleep(1.0)
        
        # 检查队列中是否有帧
        assert frame_queue.get_queue_size() > 0
        
        # 停止生产者
        producer.stop_producing()
        assert producer.is_running() is False
        
        # 获取统计信息
        stats = producer.get_stats()
        assert stats["frame_count"] > 0
        assert stats["successful_reads"] > 0
    
    @pytest.mark.asyncio
    async def test_component_monitor_integration(self):
        """测试组件监控器集成"""
        monitor = ComponentMonitor()
        await monitor.start()
        
        # 注册组件
        recovery_called = False
        shutdown_called = False
        
        async def mock_recovery():
            nonlocal recovery_called
            recovery_called = True
            return True
        
        async def mock_shutdown():
            nonlocal shutdown_called
            shutdown_called = True
        
        monitor.register_component(
            "test_component",
            recovery_callback=mock_recovery,
            shutdown_callback=mock_shutdown
        )
        
        # 更新组件状态为运行
        monitor.update_component_state("test_component", ComponentState.RUNNING)
        assert monitor.is_component_healthy("test_component") is True
        
        # 更新为错误状态
        monitor.update_component_state(
            "test_component", 
            ComponentState.ERROR,
            error_message="Test error"
        )
        
        # 等待自动恢复尝试
        await asyncio.sleep(0.5)
        
        # 验证恢复被调用
        assert recovery_called is True
        
        # 停止监控器
        await monitor.stop()
    
    @pytest.mark.asyncio
    async def test_producer_consumer_integration(self, mock_connection_manager, frame_queue):
        """测试生产者-消费者集成"""
        # 启动生产者
        producer = FrameProducer(
            connection_manager=mock_connection_manager,
            frame_queue=frame_queue,
            stream_key="test_stream"
        )

        # 设置事件循环
        current_loop = asyncio.get_running_loop()
        producer.set_event_loop(current_loop)

        producer.start_producing()
        
        # 模拟消费者
        consumed_frames = []
        consumer_running = True
        
        async def consumer():
            while consumer_running:
                frame_data = await frame_queue.get_frame(timeout=0.5)
                if frame_data:
                    consumed_frames.append(frame_data)
                await asyncio.sleep(0.1)
        
        # 启动消费者任务
        consumer_task = asyncio.create_task(consumer())
        
        # 运行一段时间
        await asyncio.sleep(2.0)
        
        # 停止消费者和生产者
        consumer_running = False
        producer.stop_producing()
        
        # 等待消费者任务完成
        await consumer_task
        
        # 验证结果
        assert len(consumed_frames) > 0
        for frame_data in consumed_frames:
            assert isinstance(frame_data, FrameData)
            assert frame_data.frame is not None
            assert frame_data.timestamp > 0
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, mock_rtmp_url, frame_queue):
        """测试错误处理集成"""
        # 创建会失败的连接管理器
        failing_manager = RTMPConnectionManager(mock_rtmp_url)
        
        error_callback_called = False
        error_message = None
        
        def error_callback(error):
            nonlocal error_callback_called, error_message
            error_callback_called = True
            error_message = str(error)
        
        producer = FrameProducer(
            connection_manager=failing_manager,
            frame_queue=frame_queue,
            stream_key="failing_stream",
            on_error=error_callback
        )

        # 设置事件循环
        current_loop = asyncio.get_running_loop()
        producer.set_event_loop(current_loop)

        # 模拟连接失败
        with patch.object(failing_manager, 'get_video_capture', return_value=None), \
             patch.object(failing_manager, 'is_connected', return_value=False):
            producer.start_producing()
            
            # 等待错误发生
            await asyncio.sleep(1.0)
            
            producer.stop_producing()
        
        # 验证错误处理
        stats = producer.get_stats()
        assert stats["total_failures"] > 0
    
    @pytest.mark.asyncio
    async def test_queue_overflow_handling(self, mock_connection_manager):
        """测试队列溢出处理"""
        # 创建小容量队列
        small_queue = AsyncFrameQueue(max_size=5, drop_old_frames=True)
        
        producer = FrameProducer(
            connection_manager=mock_connection_manager,
            frame_queue=small_queue,
            stream_key="overflow_test"
        )
        
        producer.start_producing()
        
        # 让生产者运行，但不消费帧
        await asyncio.sleep(1.0)
        
        producer.stop_producing()
        
        # 检查队列是否保持在限制内
        assert small_queue.get_queue_size() <= 5
        
        # 检查是否有丢帧
        stats = small_queue.get_stats()
        assert stats.dropped_frames >= 0  # 可能有丢帧
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self, mock_connection_manager, frame_queue):
        """测试负载下的性能"""
        # 创建多个生产者
        producers = []
        for i in range(3):
            producer = FrameProducer(
                connection_manager=mock_connection_manager,
                frame_queue=frame_queue,
                stream_key=f"load_test_{i}"
            )
            producers.append(producer)
        
        # 启动所有生产者
        for producer in producers:
            producer.start_producing()
        
        # 运行负载测试
        start_time = time.time()
        await asyncio.sleep(2.0)
        end_time = time.time()
        
        # 停止所有生产者
        for producer in producers:
            producer.stop_producing()
        
        # 检查性能指标
        total_frames = 0
        for producer in producers:
            stats = producer.get_stats()
            total_frames += stats["frame_count"]
        
        duration = end_time - start_time
        fps = total_frames / duration
        
        # 验证性能合理（这个阈值可能需要根据实际情况调整）
        assert fps > 10  # 至少10 FPS
        assert frame_queue.get_queue_size() >= 0


if __name__ == "__main__":
    pytest.main([__file__]) 