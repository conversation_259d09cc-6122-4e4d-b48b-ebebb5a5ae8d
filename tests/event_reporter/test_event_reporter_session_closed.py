# 说明：本测试用于复现并验证“Session is closed/Connection closed”问题的健壮性处理。
# - 模拟 aiohttp 会话在首次请求时抛出 RuntimeError("Session is closed") 或 ServerDisconnectedError
# - 预期：EventReporter._with_retry 能够捕获并在必要时调用 _reset_session，再次获取新会话后重试
# - 本测试不访问真实网络，仅通过 monkeypatch 注入行为

import asyncio
import types
import aiohttp
import pytest

from app.utils.event_reporter import EventReporter


class DummyResponse:
    def __init__(self, status: int = 200, text: str = "OK"):
        self.status = status
        self._text = text
        # 兼容属性
        self.request_info = None

    async def text(self):
        return self._text

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc, tb):
        return False


class DummyPostCtx:
    """可作为 async with 使用的返回对象，内部在第一次被 __aenter__ 时抛异常，用于模拟 Session is closed/连接断开"""

    def __init__(self, raise_on_enter: BaseException | None, response: DummyResponse):
        self.raise_on_enter = raise_on_enter
        self.response = response
        self._entered = False

    async def __aenter__(self):
        if not self._entered and self.raise_on_enter is not None:
            self._entered = True
            raise self.raise_on_enter
        return self.response

    async def __aexit__(self, exc_type, exc, tb):
        return False


@pytest.mark.asyncio
async def test_event_reporter_handles_session_closed(monkeypatch):
    er = EventReporter()

    # 构造一个假的 ClientSession.post 行为：第一次进入抛 RuntimeError("Session is closed")，第二次正常返回 200
    class DummySession:
        def __init__(self):
            self.closed = False
            self._count = 0

        def post(self, url, data=None, timeout=None, headers=None):
            self._count += 1
            if self._count == 1:
                # 第一次抛出“Session is closed”
                return DummyPostCtx(RuntimeError("Session is closed"), DummyResponse(200, "OK"))
            # 第二次返回正常 200
            return DummyPostCtx(None, DummyResponse(200, "OK"))

    dummy = DummySession()

    async def fake_get_session():
        return dummy

    async def fake_reset_session():
        # 将 dummy 视为失效：下一次 get_session 应返回一个新的 dummy
        pass

    monkeypatch.setattr(er, "_get_session", fake_get_session)
    monkeypatch.setattr(er, "_reset_session", fake_reset_session)

    # 定义一个最小化的调用 _with_retry 场景
    called = {
        "n": 0
    }

    async def do_post():
        called["n"] += 1
        session = await er._get_session()
        obj = session.post("http://example.com/api", data={})
        async with obj as resp:
            txt = await resp.text()
            assert resp.status == 200
            assert txt == "OK"

    ok = await er._with_retry(do_post, retries=1, base_delay=0.01)
    assert ok is True
    assert called["n"] >= 1


@pytest.mark.asyncio
async def test_report_event_returns_true_if_video_success(monkeypatch):
    """当事件上报失败但视频上报成功时，report_event 应返回 True（避免误报警告）。"""
    er = EventReporter()

    # 模拟 _with_retry：事件上报返回 False，视频上报返回 True
    async def fake_retry_fail(func, retries=2, base_delay=0.01):
        return False

    async def fake_retry_ok(func, retries=2, base_delay=0.01):
        return True

    # 简化 _get_session，避免真实网络
    async def fake_get_session():
        class S:
            closed = False

            def post(self, *a, **kw):
                return DummyPostCtx(None, DummyResponse(200, "OK"))

        return S()

    monkeypatch.setattr(er, "_get_session", fake_get_session)

    # 伪造参数
    result = {
        "drone_code": "SN123",
        "timestamp": 1234567890,
        "result": {
            "detections": [
                {"target_id": 1, "target_name": "船只", "image_paths": {"init": "/tmp/a.png", "res": "/tmp/b.png"}},
            ]
        },
        "video_path": "/tmp/ok.mp4",
    }

    class DummySP:
        drone_code = "SN123"
        frame_rate = 30

    # 替换 _with_retry：先用于事件，上层传 fake_retry_fail；用于视频，上层传 fake_retry_ok
    monkeypatch.setattr(er, "_with_retry", fake_retry_ok)

    # 直接调用 report_event，视频路径已存在字段，不会调用 save_detection_video
    ok = await er.report_event(result, DummySP())
    assert ok is True

