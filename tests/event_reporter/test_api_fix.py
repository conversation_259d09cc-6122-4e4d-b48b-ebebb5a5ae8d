"""
API修复测试 - 验证_post_cm_or_await方法修复后的效果

专注于测试修复是否解决了Connection closed问题
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from app.utils.event_reporter import EventReporter


class TestAPIFix:
    """API修复测试类"""
    
    @pytest.fixture
    def event_reporter(self):
        """创建EventReporter实例"""
        return EventReporter()
    
    @pytest.mark.asyncio
    async def test_post_method_fix(self, event_reporter):
        """测试修复后的_post_cm_or_await方法"""
        # 创建模拟的aiohttp响应
        class MockResponse:
            def __init__(self, status=200, text="OK"):
                self.status = status
                self._text = text
                self.request_info = MagicMock()
            
            async def text(self):
                return self._text
            
            async def __aenter__(self):
                return self
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
        
        # 创建模拟的session
        class MockSession:
            def post(self, url, data=None, timeout=None):
                return MockResponse(200, "Success")
        
        mock_session = MockSession()
        test_data = {"test": "data"}
        
        # 测试修复后的方法
        response = await event_reporter._post_cm_or_await(
            mock_session,
            "http://example.com",
            test_data
        )
        
        # 验证响应
        assert response.status == 200
        text = await response.text()
        assert text == "Success"
    
    @pytest.mark.asyncio
    async def test_single_api_call_success(self, event_reporter):
        """测试单个API调用成功"""
        # 模拟成功的HTTP响应
        class MockResponse:
            def __init__(self, status=200, text="OK"):
                self.status = status
                self._text = text
                self.request_info = MagicMock()
            
            async def text(self):
                return self._text
            
            async def __aenter__(self):
                return self
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
        
        class MockSession:
            def post(self, url, data=None, timeout=None):
                return MockResponse(200, "Success")
        
        # 模拟_get_session返回mock session
        with patch.object(event_reporter, '_get_session', return_value=MockSession()):
            # 测试事件上报的单个API调用
            event_data = {
                "imageinit": "test.png",
                "imageres": "test_predict.png",
                "timestamp": "1234567890",
                "err": "2",
                "camera": "fly_camera",
                "snNum": "TEST123",
                "business_type": "船只",
                "identifyType": "1"
            }
            
            # 直接调用_post_cm_or_await方法
            session = await event_reporter._get_session()
            response = await event_reporter._post_cm_or_await(
                session,
                event_reporter.event_api_url,
                event_reporter._build_form_data(event_data)
            )
            
            assert response.status == 200
            text = await response.text()
            assert text == "Success"
    
    @pytest.mark.asyncio
    async def test_complete_event_report_flow(self, event_reporter):
        """测试完整的事件上报流程"""
        # 构造测试数据
        result = {
            "drone_code": "TEST123",
            "timestamp": 1234567890000,
            "result": {
                "detections": [
                    {
                        "target_id": 1,
                        "target_name": "船只",
                        "target_category": "船只",
                        "error_code": "2",
                        "image_paths": {
                            "init": "test_init.png",
                            "res": "test_res.png"
                        }
                    }
                ]
            },
            "video_path": "test_video.mp4"
        }
        
        class MockStreamProcessor:
            drone_code = "TEST123"
            frame_rate = 30
            
            def get_frames_around_timestamp(self, timestamp, duration):
                return []
        
        # 模拟成功的HTTP响应
        class MockResponse:
            def __init__(self, status=200, text="OK"):
                self.status = status
                self._text = text
                self.request_info = MagicMock()
            
            async def text(self):
                return self._text
            
            async def __aenter__(self):
                return self
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
        
        class MockSession:
            def post(self, url, data=None, timeout=None):
                return MockResponse(200, "Success")
        
        # 模拟依赖
        with patch.object(event_reporter, '_get_session', return_value=MockSession()):
            with patch.object(event_reporter, 'drone_manager') as mock_drone_manager:
                mock_drone_manager.get_drone_info.return_value = None
                
                success = await event_reporter.report_event(result, MockStreamProcessor())
                
                assert success is True
    
    @pytest.mark.asyncio
    async def test_connection_error_retry(self, event_reporter):
        """测试连接错误重试机制"""
        call_count = 0

        async def failing_then_success():
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                # 模拟连接错误，这会触发会话重置
                from aiohttp import ServerDisconnectedError
                raise ServerDisconnectedError("Connection closed")
            # 第三次成功，不抛出异常

        # 模拟会话重置
        with patch.object(event_reporter, '_reset_session') as mock_reset:
            result = await event_reporter._with_retry(failing_then_success, retries=2, base_delay=0.01)

            assert result is True
            assert call_count == 3
            # 验证会话重置被调用了2次（前两次失败）
            assert mock_reset.call_count == 2
    
    @pytest.mark.asyncio
    async def test_multiple_consecutive_calls(self, event_reporter):
        """测试连续多次API调用"""
        # 模拟成功的HTTP响应
        class MockResponse:
            def __init__(self, status=200, text="OK"):
                self.status = status
                self._text = text
                self.request_info = MagicMock()
            
            async def text(self):
                return self._text
            
            async def __aenter__(self):
                return self
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
        
        class MockSession:
            def __init__(self):
                self.call_count = 0
            
            def post(self, url, data=None, timeout=None):
                self.call_count += 1
                return MockResponse(200, f"Success {self.call_count}")
        
        mock_session = MockSession()
        
        with patch.object(event_reporter, '_get_session', return_value=mock_session):
            # 连续调用3次
            for i in range(3):
                event_data = {
                    "imageinit": f"test_{i}.png",
                    "imageres": f"test_predict_{i}.png",
                    "timestamp": str(1234567890 + i),
                    "err": "2",
                    "camera": "fly_camera",
                    "snNum": f"TEST{i}",
                    "business_type": "船只",
                    "identifyType": "1"
                }
                
                session = await event_reporter._get_session()
                response = await event_reporter._post_cm_or_await(
                    session,
                    event_reporter.event_api_url,
                    event_reporter._build_form_data(event_data)
                )
                
                assert response.status == 200
                text = await response.text()
                assert f"Success {i+1}" in text
                
                # 等待最小间隔
                await event_reporter._sleep_min_interval()
        
        # 验证所有调用都成功
        assert mock_session.call_count == 3
