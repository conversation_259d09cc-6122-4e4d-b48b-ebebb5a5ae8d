# 测试目录结构说明

本目录包含了CV项目的所有测试文件，按照功能和用途进行了分类组织。

## 目录结构

### 📁 unit/ - 单元测试
包含针对单个组件或模块的单元测试：
- `test_config.py` - 配置管理测试
- `test_event_reporter.py` - 事件上报测试
- `test_websocket_heartbeat.py` - WebSocket心跳机制测试
- `test_yolo_model.py` - YOLO模型测试
- 其他单元测试文件

**运行方式**：
```bash
# 运行所有单元测试
python -m pytest tests/unit/ -v

# 运行特定测试文件
python -m pytest tests/unit/test_config.py -v
```

### 📁 integration/ - 集成测试
包含多个组件协同工作的集成测试：
- `test_fixes_verification.py` - 修复验证测试
- `test_frame_producer_improvements.py` - 帧生产者改进测试
- `test_stream_integration.py` - 流集成测试

**运行方式**：
```bash
# 运行集成测试（需要实际的RTMP流）
python tests/integration/test_fixes_verification.py <stream_id>
```

### 📁 stability/ - 稳定性测试
包含长时间运行和压力测试：
- `websocket_stability_test.py` - WebSocket连接稳定性测试
- `video_stream_stability_test.py` - 视频流稳定性测试
- `test_stream_stability_improvements.py` - 流稳定性改进测试
- `test_stress_performance.py` - 压力和性能测试

**运行方式**：
```bash
# WebSocket稳定性测试（10分钟）
python tests/stability/websocket_stability_test.py 10

# 视频流稳定性测试
python tests/stability/video_stream_stability_test.py "rtmp://..." 5
```

### 📁 diagnostic/ - 诊断工具
包含用于问题诊断和调试的工具：
- `rtmp_stream_diagnostic.py` - RTMP流诊断工具
- `test_event_loop_debug.py` - 事件循环调试测试
- `test_rtmp_stream_simple.py` - 简单RTMP流测试
- `check_env.py` - 环境检查工具
- `debug_*.py` - 各种调试工具

**运行方式**：
```bash
# RTMP流诊断
python tests/diagnostic/rtmp_stream_diagnostic.py "rtmp://..." 30

# 环境检查
python tests/diagnostic/check_env.py
```

### 📁 manual/ - 手动测试
预留目录，用于需要人工干预的手动测试脚本。

## 测试分类说明

### 🔧 单元测试 (Unit Tests)
- **目的**：测试单个函数、类或模块的功能
- **特点**：快速执行，不依赖外部资源
- **运行频率**：每次代码提交前

### 🔗 集成测试 (Integration Tests)
- **目的**：测试多个组件之间的协作
- **特点**：可能需要外部资源（如RTMP流）
- **运行频率**：功能开发完成后

### 🏃 稳定性测试 (Stability Tests)
- **目的**：测试系统在长时间运行或高负载下的表现
- **特点**：运行时间较长，模拟真实使用场景
- **运行频率**：版本发布前

### 🔍 诊断工具 (Diagnostic Tools)
- **目的**：帮助开发者诊断和调试问题
- **特点**：提供详细的调试信息和状态检查
- **运行频率**：出现问题时按需运行

### 运行特定测试文件

```bash
# 运行特定测试文件
pytest -xvs tests/unit/app/utils/test_event_reporter.py
pytest -xvs tests/unit/app/processor/test_image_processor.py
```

### 运行特定测试函数

```bash
# 运行特定测试函数
pytest -xvs tests/unit/app/utils/test_event_reporter.py::test_save_detection_video
pytest -xvs tests/unit/app/processor/test_image_processor.py::test_super_resolution
```

## 测试依赖

测试需要以下依赖：
- pytest
- pytest-asyncio (用于测试异步函数)

可以通过以下命令安装：

```bash
pip install pytest pytest-asyncio
```

## 日志和调试

测试代码中添加了详细的日志记录，以帮助调试测试过程中的问题。日志级别默认设置为 INFO。

### 查看更详细的日志

如果需要查看更详细的日志，可以设置环境变量 `PYTEST_LOG_LEVEL=DEBUG`：

```bash
PYTEST_LOG_LEVEL=DEBUG python tests/run_tests.py
```

或者直接使用 pytest 的 `-v` 或 `-vv` 参数：

```bash
pytest -vv tests/unit
```

### 调试特定测试

提供了几个专门的调试脚本：

1. **环境检查脚本**：检查测试环境是否正确设置
   ```bash
   python tests/check_env.py
   ```

2. **通用调试脚本**：提供详细的调试信息
   ```bash
   python tests/debug_tests.py
   ```

3. **ImageProcessor调试脚本**：专门调试ImageProcessor测试
   ```bash
   python tests/debug_image_processor.py
   ```

4. **超分辨率测试调试脚本**：专门调试超分辨率测试
   ```bash
   python tests/debug_super_resolution.py
   ```

5. **EventReporter测试脚本**：专门运行EventReporter测试
   ```bash
   python tests/test_event_reporter.py
   ```

### 常见问题排查

1. **找不到测试目录**：确保在项目根目录下运行测试命令，或者使用绝对路径指定测试目录。

2. **导入错误**：检查 `conftest.py` 中的路径设置，确保项目根目录已添加到 Python 路径中。

3. **配置文件错误**：确保 `config_fake.yaml` 文件存在并包含必要的配置项。

4. **测试依赖缺失**：确保已安装所有必要的测试依赖。

5. **Mock对象问题**：如果测试断言失败，检查是否正确模拟了被测试对象的属性和方法。特别是对于布尔值属性，确保使用实际的布尔值而不是MagicMock对象。

6. **图像尺寸问题**：在超分辨率测试中，确保模拟的超分辨率模型返回正确尺寸的图像。测试期望输出图像尺寸为输入的2倍。

## 注意事项

1. 测试使用 `config_fake.yaml` 作为配置文件，确保该文件存在并包含必要的配置项。
2. 测试使用模拟对象（mock）来隔离被测试的代码，避免对实际系统产生影响。
3. 异步函数的测试使用 `@pytest.mark.asyncio` 装饰器。 