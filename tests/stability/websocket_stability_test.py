#!/usr/bin/env python3
"""
WebSocket稳定性测试脚本
用于验证新的WebSocket连接稳定性改进
"""
import asyncio
import logging
import signal
import sys
from app.database.websocket_client import WebSocketClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WebSocketStabilityTest:
    """WebSocket稳定性测试类"""
    
    def __init__(self):
        self.ws_client = WebSocketClient()
        self.running = True
        self.stats = {
            'connections': 0,
            'disconnections': 0,
            'messages_received': 0,
            'start_time': None
        }
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，正在停止测试...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def message_handler(self, data):
        """消息处理器"""
        self.stats['messages_received'] += 1
        if self.stats['messages_received'] % 10 == 0:
            logger.info(f"已接收 {self.stats['messages_received']} 条消息")
    
    async def monitor_connection(self):
        """监控连接状态"""
        last_connection_state = False
        
        while self.running:
            current_state = self.ws_client.ws is not None and not self.ws_client.ws.closed
            
            if current_state != last_connection_state:
                if current_state:
                    self.stats['connections'] += 1
                    logger.info(f"WebSocket连接建立 (第 {self.stats['connections']} 次)")
                else:
                    self.stats['disconnections'] += 1
                    logger.info(f"WebSocket连接断开 (第 {self.stats['disconnections']} 次)")
                
                last_connection_state = current_state
            
            await asyncio.sleep(5)
    
    def print_stats(self):
        """打印统计信息"""
        if self.stats['start_time']:
            duration = asyncio.get_event_loop().time() - self.stats['start_time']
            logger.info("=== WebSocket稳定性测试统计 ===")
            logger.info(f"测试时长: {duration:.1f} 秒")
            logger.info(f"连接次数: {self.stats['connections']}")
            logger.info(f"断开次数: {self.stats['disconnections']}")
            logger.info(f"接收消息数: {self.stats['messages_received']}")
            if duration > 0:
                logger.info(f"平均消息率: {self.stats['messages_received']/duration:.2f} 消息/秒")
            if self.stats['connections'] > 0:
                logger.info(f"连接稳定性: {(1 - self.stats['disconnections']/self.stats['connections'])*100:.1f}%")
    
    async def run_test(self, duration_minutes=10):
        """运行稳定性测试"""
        logger.info(f"开始WebSocket稳定性测试，持续时间: {duration_minutes} 分钟")
        
        # 设置信号处理器
        self.setup_signal_handlers()
        
        # 注册消息处理器
        self.ws_client.register_message_handler('test_handler', self.message_handler)
        
        # 记录开始时间
        self.stats['start_time'] = asyncio.get_event_loop().time()
        
        try:
            # 启动WebSocket客户端和监控任务
            tasks = [
                asyncio.create_task(self.ws_client.start()),
                asyncio.create_task(self.monitor_connection())
            ]
            
            # 等待指定时间或收到停止信号
            end_time = asyncio.get_event_loop().time() + duration_minutes * 60
            
            while self.running and asyncio.get_event_loop().time() < end_time:
                await asyncio.sleep(1)
            
            logger.info("测试时间结束或收到停止信号，正在停止...")
            
        except Exception as e:
            logger.error(f"测试过程中发生异常: {str(e)}")
        
        finally:
            # 停止WebSocket客户端
            await self.ws_client.stop()
            
            # 取消所有任务
            for task in tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            
            # 打印统计信息
            self.print_stats()

async def main():
    """主函数"""
    if len(sys.argv) > 1:
        try:
            duration = int(sys.argv[1])
        except ValueError:
            logger.error("请提供有效的测试时长（分钟）")
            sys.exit(1)
    else:
        duration = 10  # 默认10分钟
    
    test = WebSocketStabilityTest()
    await test.run_test(duration)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)
