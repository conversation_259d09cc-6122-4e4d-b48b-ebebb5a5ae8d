#!/usr/bin/env python3
"""
视频流稳定性测试脚本
用于验证视频流空帧处理和稳定性改进
"""
import asyncio
import logging
import signal
import sys
import time
from typing import Dict, Any
from app.video.streams.rtmp import RTMPStream
from app.video.streams.rtmp_connection_manager import RTMPConnectionManager
from app.utils.async_frame_queue import AsyncFrameQueue

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class VideoStreamStabilityTest:
    """视频流稳定性测试类"""
    
    def __init__(self, rtmp_url: str):
        self.rtmp_url = rtmp_url
        self.running = True
        self.stats = {
            'start_time': None,
            'total_frames': 0,
            'successful_frames': 0,
            'empty_frames': 0,
            'failures': 0,
            'reconnections': 0,
            'last_frame_time': 0
        }
        self.stream = None
        
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，正在停止测试...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def frame_handler(self, frame_data, is_timeout=False):
        """帧处理器"""
        if frame_data is not None:
            self.stats['successful_frames'] += 1
            self.stats['last_frame_time'] = time.time()
            # 每1000个成功帧记录一次
            if self.stats['successful_frames'] % 1000 == 0:
                logger.info(f"已成功获取 {self.stats['successful_frames']} 帧")
        elif is_timeout:
            # 超时不计入统计，只是等待
            return
        else:
            self.stats['empty_frames'] += 1

        self.stats['total_frames'] += 1

        # 每10000帧记录一次统计（减少日志输出）
        if self.stats['total_frames'] % 10000 == 0:
            self.print_current_stats()
    
    def print_current_stats(self):
        """打印当前统计信息"""
        if self.stats['start_time']:
            duration = time.time() - self.stats['start_time']
            fps = self.stats['successful_frames'] / duration if duration > 0 else 0
            empty_rate = (self.stats['empty_frames'] / self.stats['total_frames'] * 100) if self.stats['total_frames'] > 0 else 0
            
            logger.info(
                f"实时统计 - 总帧数: {self.stats['total_frames']}, "
                f"成功帧: {self.stats['successful_frames']}, "
                f"空帧: {self.stats['empty_frames']} ({empty_rate:.1f}%), "
                f"FPS: {fps:.2f}, "
                f"运行时长: {duration:.1f}秒"
            )
    
    def print_final_stats(self):
        """打印最终统计信息"""
        if self.stats['start_time']:
            duration = time.time() - self.stats['start_time']
            logger.info("=== 视频流稳定性测试最终统计 ===")
            logger.info(f"测试时长: {duration:.1f} 秒")
            logger.info(f"总帧数: {self.stats['total_frames']}")
            logger.info(f"成功帧数: {self.stats['successful_frames']}")
            logger.info(f"空帧数: {self.stats['empty_frames']}")
            logger.info(f"失败数: {self.stats['failures']}")
            logger.info(f"重连次数: {self.stats['reconnections']}")
            
            if duration > 0:
                fps = self.stats['successful_frames'] / duration
                empty_rate = (self.stats['empty_frames'] / self.stats['total_frames'] * 100) if self.stats['total_frames'] > 0 else 0
                success_rate = (self.stats['successful_frames'] / self.stats['total_frames'] * 100) if self.stats['total_frames'] > 0 else 0
                
                logger.info(f"平均FPS: {fps:.2f}")
                logger.info(f"空帧率: {empty_rate:.1f}%")
                logger.info(f"成功率: {success_rate:.1f}%")
    
    async def monitor_stream_stats(self):
        """监控流统计信息"""
        last_producer_running = True

        while self.running:
            if self.stream and hasattr(self.stream, 'frame_producer') and self.stream.frame_producer:
                producer_stats = self.stream.frame_producer.get_stats()

                # 更新失败统计
                if producer_stats['total_failures'] != self.stats['failures']:
                    self.stats['failures'] = producer_stats['total_failures']
                    logger.warning(f"检测到失败增加，当前总失败数: {self.stats['failures']}")

                # 检测重连
                current_running = producer_stats['is_running']
                if last_producer_running and not current_running:
                    self.stats['reconnections'] += 1
                    logger.warning(f"检测到流断开，当前重连次数: {self.stats['reconnections']}")
                elif not last_producer_running and current_running:
                    logger.info(f"检测到流重新连接")

                last_producer_running = current_running

                # 定期输出详细统计
                if hasattr(self, '_last_detailed_log'):
                    if time.time() - self._last_detailed_log > 30:  # 每30秒
                        self._log_detailed_stats(producer_stats)
                        self._last_detailed_log = time.time()
                else:
                    self._last_detailed_log = time.time()
            else:
                logger.warning("流或生产者不可用")

            await asyncio.sleep(5)

    def _log_detailed_stats(self, producer_stats):
        """记录详细统计信息"""
        logger.info(f"=== 详细统计 ===")
        logger.info(f"生产者运行状态: {producer_stats['is_running']}")
        logger.info(f"生产者帧数: {producer_stats['frame_count']}")
        logger.info(f"生产者成功读取: {producer_stats['successful_reads']}")
        logger.info(f"生产者失败: {producer_stats['total_failures']}")
        logger.info(f"生产者空帧: {producer_stats.get('total_empty_frames', 'N/A')}")
        logger.info(f"队列大小: {producer_stats['queue_size']}")
        logger.info(f"测试成功帧: {self.stats['successful_frames']}")
        logger.info(f"测试空帧: {self.stats['empty_frames']}")
    
    async def run_test(self, duration_minutes=10):
        """运行稳定性测试"""
        logger.info(f"开始视频流稳定性测试，RTMP URL: {self.rtmp_url}")
        logger.info(f"测试时长: {duration_minutes} 分钟")
        
        # 设置信号处理器
        self.setup_signal_handlers()
        
        # 记录开始时间
        self.stats['start_time'] = time.time()
        
        try:
            # 创建RTMP流
            self.stream = RTMPStream()
            
            # 启动流
            await self.stream.start("test_stream")
            logger.info("视频流已启动")
            
            # 启动监控任务
            monitor_task = asyncio.create_task(self.monitor_stream_stats())
            
            # 等待指定时间或收到停止信号
            end_time = time.time() + duration_minutes * 60
            
            # 等待流启动
            await asyncio.sleep(2)
            logger.info("开始获取帧...")

            frame_get_count = 0
            while self.running and time.time() < end_time:
                try:
                    # 获取帧，使用较短的超时
                    frame_data = await asyncio.wait_for(
                        self.stream.get_frame(),
                        timeout=0.5
                    )
                    await self.frame_handler(frame_data)
                    frame_get_count += 1

                    # 每1000次获取记录一次
                    if frame_get_count % 1000 == 0:
                        logger.debug(f"已尝试获取帧 {frame_get_count} 次")

                except asyncio.TimeoutError:
                    # 超时不计入统计，继续尝试
                    await self.frame_handler(None, is_timeout=True)

                except Exception as e:
                    logger.error(f"获取帧时发生异常: {str(e)}")
                    self.stats['failures'] += 1
                    await asyncio.sleep(0.1)
            
            logger.info("测试时间结束或收到停止信号，正在停止...")
            
        except Exception as e:
            logger.error(f"测试过程中发生异常: {str(e)}")
        
        finally:
            # 停止流
            if self.stream:
                await self.stream.stop()
            
            # 取消监控任务
            if 'monitor_task' in locals():
                monitor_task.cancel()
                try:
                    await monitor_task
                except asyncio.CancelledError:
                    pass
            
            # 打印最终统计信息
            self.print_final_stats()

async def main():
    """主函数"""
    if len(sys.argv) < 2:
        logger.error("请提供RTMP URL")
        logger.info("用法: python video_stream_stability_test.py <rtmp_url> [duration_minutes]")
        sys.exit(1)
    
    rtmp_url = sys.argv[1]
    
    if len(sys.argv) > 2:
        try:
            duration = int(sys.argv[2])
        except ValueError:
            logger.error("请提供有效的测试时长（分钟）")
            sys.exit(1)
    else:
        duration = 5  # 默认5分钟
    
    test = VideoStreamStabilityTest(rtmp_url)
    await test.run_test(duration)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)
