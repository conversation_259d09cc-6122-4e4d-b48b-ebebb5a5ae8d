#!/usr/bin/env python3
"""
测试流稳定性改进效果
模拟网络不稳定情况下的流处理
"""
import asyncio
import logging
import time
import sys
import signal
from app.processor.stream_processor import StreamProcessor
from app.video.streams.rtmp import RTMPStream

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StreamStabilityTest:
    """流稳定性测试类"""
    
    def __init__(self, stream_id: str):
        self.stream_id = stream_id
        self.running = True
        self.stats = {
            'start_time': None,
            'stream_restarts': 0,
            'processor_restarts': 0,
            'total_frames_processed': 0,
            'last_restart_time': 0
        }
        self.stream = None
        self.processor = None
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，正在停止测试...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def create_stream(self):
        """创建RTMP流"""
        try:
            self.stream = RTMPStream()
            success = await self.stream.start(self.stream_id)
            if success:
                logger.info(f"成功创建流: {self.stream_id}")
                return True
            else:
                logger.error(f"创建流失败: {self.stream_id}")
                return False
        except Exception as e:
            logger.error(f"创建流时发生异常: {str(e)}")
            return False
    
    async def create_processor(self):
        """创建流处理器"""
        try:
            if not self.stream:
                return False
            
            self.processor = StreamProcessor(
                drone_code=self.stream_id,
                stream_id=f"{self.stream_id}_drone",
                stream_type="drone"
            )

            success = await self.processor.start(self.stream)
            if success:
                logger.info(f"成功创建处理器: {self.stream_id}")
                return True
            else:
                logger.error(f"创建处理器失败: {self.stream_id}")
                return False
        except Exception as e:
            logger.error(f"创建处理器时发生异常: {str(e)}")
            return False
    
    async def monitor_stream_health(self):
        """监控流健康状态"""
        while self.running:
            try:
                if self.stream:
                    is_healthy = await self.stream.check_stream()
                    if not is_healthy:
                        logger.warning("检测到流不健康")
                    
                    # 获取流统计
                    if hasattr(self.stream, 'frame_producer') and self.stream.frame_producer:
                        stats = self.stream.frame_producer.get_stats()
                        if stats['frame_count'] != self.stats['total_frames_processed']:
                            frame_delta = stats['frame_count'] - self.stats['total_frames_processed']
                            self.stats['total_frames_processed'] = stats['frame_count']
                            logger.info(f"流统计更新: +{frame_delta} 帧, 总计: {stats['frame_count']}")
                
                if self.processor:
                    if not self.processor.is_running():
                        logger.warning("检测到处理器已停止")
                        self.stats['processor_restarts'] += 1
                        self.stats['last_restart_time'] = time.time()
                        
                        # 尝试重启处理器
                        logger.info("尝试重启处理器...")
                        await self.cleanup_processor()
                        await asyncio.sleep(2)  # 等待清理完成
                        
                        if await self.create_processor():
                            logger.info("处理器重启成功")
                        else:
                            logger.error("处理器重启失败")
                
            except Exception as e:
                logger.error(f"监控过程中发生异常: {str(e)}")
            
            await asyncio.sleep(10)  # 每10秒检查一次
    
    async def cleanup_stream(self):
        """清理流资源"""
        if self.stream:
            try:
                await self.stream.stop()
                logger.info("流已停止")
            except Exception as e:
                logger.error(f"停止流时发生异常: {str(e)}")
            finally:
                self.stream = None
    
    async def cleanup_processor(self):
        """清理处理器资源"""
        if self.processor:
            try:
                await self.processor.stop()
                logger.info("处理器已停止")
            except Exception as e:
                logger.error(f"停止处理器时发生异常: {str(e)}")
            finally:
                self.processor = None
    
    def print_stats(self):
        """打印统计信息"""
        if self.stats['start_time']:
            duration = time.time() - self.stats['start_time']
            logger.info("=== 流稳定性测试统计 ===")
            logger.info(f"测试时长: {duration:.1f} 秒")
            logger.info(f"流重启次数: {self.stats['stream_restarts']}")
            logger.info(f"处理器重启次数: {self.stats['processor_restarts']}")
            logger.info(f"总处理帧数: {self.stats['total_frames_processed']}")
            
            if duration > 0:
                avg_fps = self.stats['total_frames_processed'] / duration
                logger.info(f"平均FPS: {avg_fps:.2f}")
                
                if self.stats['last_restart_time'] > 0:
                    time_since_restart = duration - (self.stats['last_restart_time'] - self.stats['start_time'])
                    logger.info(f"距离上次重启: {time_since_restart:.1f} 秒")
    
    async def run_test(self, duration_minutes=15):
        """运行稳定性测试"""
        logger.info(f"开始流稳定性测试，流ID: {self.stream_id}")
        logger.info(f"测试时长: {duration_minutes} 分钟")
        
        # 设置信号处理器
        self.setup_signal_handlers()
        
        # 记录开始时间
        self.stats['start_time'] = time.time()
        
        try:
            # 创建流和处理器
            if not await self.create_stream():
                logger.error("无法创建流，测试结束")
                return
            
            if not await self.create_processor():
                logger.error("无法创建处理器，测试结束")
                return
            
            # 启动监控任务
            monitor_task = asyncio.create_task(self.monitor_stream_health())
            
            # 等待指定时间或收到停止信号
            end_time = time.time() + duration_minutes * 60
            
            while self.running and time.time() < end_time:
                await asyncio.sleep(1)
            
            logger.info("测试时间结束或收到停止信号，正在停止...")
            
        except Exception as e:
            logger.error(f"测试过程中发生异常: {str(e)}")
        
        finally:
            # 停止监控任务
            if 'monitor_task' in locals():
                monitor_task.cancel()
                try:
                    await monitor_task
                except asyncio.CancelledError:
                    pass
            
            # 清理资源
            await self.cleanup_processor()
            await self.cleanup_stream()
            
            # 打印最终统计
            self.print_stats()

async def main():
    """主函数"""
    if len(sys.argv) < 2:
        logger.error("请提供流ID")
        logger.info("用法: python test_stream_stability_improvements.py <stream_id> [duration_minutes]")
        sys.exit(1)
    
    stream_id = sys.argv[1]
    
    if len(sys.argv) > 2:
        try:
            duration = int(sys.argv[2])
        except ValueError:
            logger.error("请提供有效的测试时长（分钟）")
            sys.exit(1)
    else:
        duration = 15  # 默认15分钟
    
    test = StreamStabilityTest(stream_id)
    await test.run_test(duration)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)
