#!/usr/bin/env python3
"""
统一的测试运行脚本
支持运行不同类型的测试
"""
import argparse
import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    print(f"运行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(result.stderr, file=sys.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"运行命令失败: {e}", file=sys.stderr)
        return False

def run_unit_tests():
    """运行单元测试"""
    print("🔧 运行单元测试...")
    return run_command([
        sys.executable, "-m", "pytest", 
        "tests/unit/", "-v", "--tb=short"
    ])

def run_integration_tests(stream_id=None):
    """运行集成测试"""
    print("🔗 运行集成测试...")
    if not stream_id:
        print("⚠️  集成测试需要流ID，跳过...")
        return True
    
    success = True
    
    # 运行修复验证测试
    print("运行修复验证测试...")
    if not run_command([
        sys.executable, "tests/integration/test_fixes_verification.py", 
        stream_id, "basic"
    ]):
        success = False
    
    return success

def run_stability_tests(stream_id=None, duration=5):
    """运行稳定性测试"""
    print("🏃 运行稳定性测试...")
    success = True
    
    # WebSocket稳定性测试
    print("运行WebSocket稳定性测试...")
    if not run_command([
        sys.executable, "tests/stability/websocket_stability_test.py", 
        str(duration)
    ]):
        success = False
    
    # 视频流稳定性测试（如果提供了流ID）
    if stream_id:
        print("运行视频流稳定性测试...")
        if not run_command([
            sys.executable, "tests/stability/test_stream_stability_improvements.py", 
            stream_id, str(duration)
        ]):
            success = False
    else:
        print("⚠️  视频流稳定性测试需要流ID，跳过...")
    
    return success

def run_diagnostic_tests():
    """运行诊断测试"""
    print("🔍 运行诊断测试...")
    success = True
    
    # 环境检查
    print("运行环境检查...")
    if not run_command([sys.executable, "tests/diagnostic/check_env.py"]):
        success = False
    
    return success

def main():
    parser = argparse.ArgumentParser(description="运行CV项目测试")
    parser.add_argument("--type", choices=["unit", "integration", "stability", "diagnostic", "all"], 
                       default="unit", help="测试类型")
    parser.add_argument("--stream-id", help="RTMP流ID（集成和稳定性测试需要）")
    parser.add_argument("--duration", type=int, default=5, 
                       help="稳定性测试持续时间（分钟）")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 切换到项目根目录
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    print(f"🚀 开始运行 {args.type} 测试...")
    print(f"工作目录: {os.getcwd()}")
    
    success = True
    
    if args.type == "unit" or args.type == "all":
        if not run_unit_tests():
            success = False
    
    if args.type == "integration" or args.type == "all":
        if not run_integration_tests(args.stream_id):
            success = False
    
    if args.type == "stability" or args.type == "all":
        if not run_stability_tests(args.stream_id, args.duration):
            success = False
    
    if args.type == "diagnostic" or args.type == "all":
        if not run_diagnostic_tests():
            success = False
    
    if success:
        print("✅ 所有测试通过！")
        sys.exit(0)
    else:
        print("❌ 部分测试失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
