# 测试文件索引

## 快速导航

### 🔧 单元测试 (tests/unit/)
| 文件名 | 描述 | 测试内容 |
|--------|------|----------|
| `test_async_frame_queue.py` | 异步帧队列测试 | 队列操作、并发安全 |
| `test_event_reporter.py` | 事件上报测试 | 事件记录、文件操作 |
| `test_websocket_heartbeat.py` | WebSocket心跳测试 | 心跳机制、连接保活 |
| `test_websocket_reconnect.py` | WebSocket重连测试 | 断线重连、错误恢复 |
| `test_frame_producer_fix.py` | 帧生产者修复测试 | 生产者逻辑、错误处理 |
| `test_confidence_threshold.py` | 置信度阈值测试 | 检测阈值、过滤逻辑 |
| `test_file_transfer.py` | 文件传输测试 | 文件上传、下载 |
| `test_shared_state_only.py` | 共享状态测试 | 状态管理、并发访问 |
| `test_active_drones_fix.py` | 活跃无人机修复测试 | 无人机状态管理 |

### 🔗 集成测试 (tests/integration/)
| 文件名 | 描述 | 需要资源 |
|--------|------|----------|
| `test_fixes_verification.py` | 修复验证测试 | RTMP流 |
| `test_frame_producer_improvements.py` | 帧生产者改进测试 | RTMP流 |
| `test_stream_integration.py` | 流集成测试 | RTMP流 |

### 🏃 稳定性测试 (tests/stability/)
| 文件名 | 描述 | 运行时间 | 需要资源 |
|--------|------|----------|----------|
| `websocket_stability_test.py` | WebSocket稳定性 | 可配置 | WebSocket服务 |
| `video_stream_stability_test.py` | 视频流稳定性 | 可配置 | RTMP流 |
| `test_stream_stability_improvements.py` | 流稳定性改进 | 可配置 | RTMP流 |
| `test_stress_performance.py` | 压力性能测试 | 较长 | 系统资源 |

### 🔍 诊断工具 (tests/diagnostic/)
| 文件名 | 描述 | 用途 |
|--------|------|------|
| `check_env.py` | 环境检查 | 验证依赖、配置 |
| `check_image_processor.py` | 图像处理器检查 | 模型加载、推理 |
| `rtmp_stream_diagnostic.py` | RTMP流诊断 | 流连接、数据分析 |
| `test_event_loop_debug.py` | 事件循环调试 | 异步问题诊断 |
| `test_rtmp_stream_simple.py` | 简单RTMP测试 | 基础流功能 |
| `debug_image_processor.py` | 图像处理调试 | 处理流程分析 |
| `debug_super_resolution.py` | 超分辨率调试 | 模型性能分析 |
| `debug_tests.py` | 通用调试工具 | 问题排查 |

## 运行建议

### 开发阶段
```bash
# 快速单元测试
python tests/run_all_tests.py --type unit

# 或使用pytest
python -m pytest tests/unit/ -v
```

### 功能验证
```bash
# 集成测试（需要流ID）
python tests/run_all_tests.py --type integration --stream-id "YOUR_STREAM_ID"
```

### 发布前检查
```bash
# 完整测试套件（需要流ID）
python tests/run_all_tests.py --type all --stream-id "YOUR_STREAM_ID" --duration 10
```

### 问题诊断
```bash
# 环境检查
python tests/diagnostic/check_env.py

# RTMP流问题
python tests/diagnostic/rtmp_stream_diagnostic.py "rtmp://..." 30

# 事件循环问题
python tests/diagnostic/test_event_loop_debug.py 30
```

## 测试数据要求

### RTMP流ID格式
- 格式：`1581F6Q8X251H00G04Z4` (16位字符)
- 来源：无人机系统或测试环境

### 测试环境
- Python 3.10+
- 所有依赖已安装 (`pip install -r requirements.txt`)
- 网络连接（用于RTMP和WebSocket测试）

## 故障排除

### 常见问题
1. **导入错误**：确保在项目根目录运行测试
2. **RTMP连接失败**：检查流ID和网络连接
3. **WebSocket连接失败**：确认WebSocket服务运行
4. **模型加载失败**：检查模型文件路径和权限

### 调试步骤
1. 运行环境检查：`python tests/diagnostic/check_env.py`
2. 检查具体组件：使用对应的diagnostic工具
3. 查看详细日志：添加 `-v` 或 `--verbose` 参数
