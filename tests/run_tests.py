#!/usr/bin/env python
"""
运行单元测试的脚本
"""
import os
import sys
import pytest
import logging
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, root_dir)
logger.info(f"项目根目录: {root_dir}")

# 确保使用config_fake.yaml作为配置文件
os.environ['CONFIG_FILE'] = 'config.yaml'
logger.info(f"使用配置文件: {os.environ['CONFIG_FILE']}")

# 获取测试目录的完整路径
test_dir = os.path.dirname(os.path.abspath(__file__))
unit_dir = os.path.join(test_dir, 'unit')
logger.info(f"测试目录: {test_dir}")
logger.info(f"单元测试目录: {unit_dir}")
logger.info(f"单元测试目录存在: {os.path.exists(unit_dir)}")

def run_event_reporter_tests():
    """只运行event_reporter.py的测试"""
    logger.info("只运行event_reporter.py的测试...")
    
    # 获取event_reporter测试文件的路径
    event_reporter_test_path = os.path.join(unit_dir, 'app', 'utils', 'test_event_reporter.py')
    
    if not os.path.exists(event_reporter_test_path):
        logger.error(f"未找到event_reporter测试文件: {event_reporter_test_path}")
        return 1
    
    logger.info(f"找到event_reporter测试文件: {event_reporter_test_path}")
    
    # 运行event_reporter测试
    return pytest.main(["-xvs", event_reporter_test_path])

def run_image_processor_tests():
    """只运行image_processor.py的测试"""
    logger.info("只运行image_processor.py的测试...")
    
    # 获取image_processor测试文件的路径
    image_processor_test_path = os.path.join(unit_dir, 'app', 'processor', 'test_image_processor.py')
    
    if not os.path.exists(image_processor_test_path):
        logger.error(f"未找到image_processor测试文件: {image_processor_test_path}")
        return 1
    
    logger.info(f"找到image_processor测试文件: {image_processor_test_path}")
    
    # 运行image_processor测试
    return pytest.main(["-xvs", image_processor_test_path])

def run_all_tests():
    """运行所有测试"""
    logger.info("运行所有测试...")
    return pytest.main(["-xvs", unit_dir])

if __name__ == "__main__":
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='运行单元测试')
    parser.add_argument('--module', choices=['all', 'event_reporter', 'image_processor'], 
                        default='all', help='指定要测试的模块')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 列出测试目录中的文件和子目录
    logger.info("测试目录内容:")
    for item in os.listdir(test_dir):
        item_path = os.path.join(test_dir, item)
        if os.path.isdir(item_path):
            logger.info(f"  目录: {item}")
        else:
            logger.info(f"  文件: {item}")
    
    # 根据指定的模块运行测试
    if args.module == 'event_reporter':
        result = run_event_reporter_tests()
    elif args.module == 'image_processor':
        result = run_image_processor_tests()
    else:  # 'all'
        result = run_all_tests()
    
    # 退出程序，返回测试结果
    sys.exit(result) 