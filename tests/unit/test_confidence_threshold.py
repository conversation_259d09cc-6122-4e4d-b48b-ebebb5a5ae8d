#!/usr/bin/env python3
"""
测试confidence_threshold功能的脚本
加载config_fake.yaml配置，使用YOLO模型识别app_data/test目录中的图片
"""

import os
import sys
import logging
import glob
import cv2
import numpy as np
import yaml
import asyncio
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, root_dir)
logger.info(f"项目根目录: {root_dir}")

def load_config_fake():
    """直接加载config_fake.yaml配置文件"""
    try:
        config_path = os.path.join(root_dir, 'config_fake.yaml')
        logger.info(f"加载配置文件: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        logger.info("成功加载config_fake.yaml配置文件")
        
        # 打印检测目标配置
        targets = config_data['detection']['targets']
        logger.info(f"加载了 {len(targets)} 个检测目标:")
        for target in targets:
            logger.info(f"  - {target['name']} (ID: {target['id']}): confidence_threshold = {target.get('confidence_threshold', '未设置')}")
        
        return config_data
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def get_test_images():
    """获取测试图片列表"""
    test_dir = os.path.join(root_dir, 'app_data', 'test')
    if not os.path.exists(test_dir):
        logger.error(f"测试目录不存在: {test_dir}")
        return []
    
    # 支持的图片格式
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(test_dir, ext)))
        image_files.extend(glob.glob(os.path.join(test_dir, ext.upper())))
    
    logger.info(f"找到 {len(image_files)} 个测试图片:")
    for img_file in image_files:
        logger.info(f"  - {os.path.basename(img_file)}")
    
    return sorted(image_files)

async def test_single_image_detection(image_path, config_data):
    """测试单张图片的检测"""
    logger.info(f"\n开始检测图片: {os.path.basename(image_path)}")
    
    try:
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            logger.error(f"无法读取图片: {image_path}")
            return None
        
        logger.info(f"图片尺寸: {image.shape}")
        
        # 初始化图像处理器
        from app.processor.image_processor import ImagePreprocessor
        processor = ImagePreprocessor(model_type='cv')
        
        # 获取检测目标配置
        targets = config_data['detection']['targets']
        
        # 进行多目标检测
        logger.info("开始多目标检测...")
        result = await processor.detect_targets(image, targets)
        
        if result and result.get("detections"):
            logger.info(f"✓ 检测到 {len(result['detections'])} 个目标:")
            for detection in result['detections']:
                logger.info(f"  - {detection['target_name']}: 置信度 {detection['confidence']:.3f} (阈值: {detection['confidence_threshold']})")
            
            # 保存标注图片
            if result.get("image_paths"):
                logger.info(f"✓ 已保存标注图片:")
                logger.info(f"  - 原始图片: {result['image_paths']['init']}")
                logger.info(f"  - 标注图片: {result['image_paths']['res']}")
        else:
            logger.info("✗ 未检测到任何目标")
        
        return result
        
    except Exception as e:
        logger.error(f"检测图片失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_individual_targets(image_path, config_data):
    """测试单个目标的检测"""
    logger.info(f"\n测试单个目标检测: {os.path.basename(image_path)}")
    
    try:
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            logger.error(f"无法读取图片: {image_path}")
            return
        
        # 初始化图像处理器
        from app.processor.image_processor import ImagePreprocessor
        processor = ImagePreprocessor(model_type='cv')
        
        # 获取检测目标配置
        targets = config_data['detection']['targets']
        
        # 对每个目标进行单独检测
        for target in targets:
            logger.info(f"\n检测目标: {target['name']} (阈值: {target.get('confidence_threshold', 0.5)})")
            
            result = processor.detect(
                image, 
                target['keyword'], 
                target.get('confidence_threshold', 0.5)
            )
            
            if result and result.get("detections"):
                logger.info(f"  ✓ 检测到 {len(result['detections'])} 个 {target['name']}")
                for detection in result['detections']:
                    logger.info(f"    - 置信度: {detection['confidence']:.3f}")
            else:
                logger.info(f"  ✗ 未检测到 {target['name']}")
        
    except Exception as e:
        logger.error(f"单个目标检测失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    logger.info("开始测试confidence_threshold功能...")
    
    # 加载配置
    config_data = load_config_fake()
    if not config_data:
        logger.error("配置加载失败，退出测试")
        return
    
    # 获取测试图片
    test_images = get_test_images()
    if not test_images:
        logger.error("没有找到测试图片，退出测试")
        return
    
    # 测试每张图片
    for image_path in test_images:
        logger.info(f"\n{'='*60}")
        logger.info(f"处理图片: {os.path.basename(image_path)}")
        logger.info(f"{'='*60}")
        
        # 测试多目标检测
        await test_single_image_detection(image_path, config_data)
        
        # 测试单个目标检测
        test_individual_targets(image_path, config_data)
    
    logger.info(f"\n{'='*60}")
    logger.info("测试完成")
    logger.info(f"{'='*60}")

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main()) 