"""
WebSocket连接稳定性单元测试
"""
import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from app.database.websocket_client import WebSocketClient


class TestWebSocketStability:
    """WebSocket连接稳定性测试类"""

    @pytest.fixture
    def ws_client(self):
        """创建WebSocket客户端实例"""
        with patch('app.database.websocket_client.config') as mock_config:
            # 模拟配置
            mock_config.WEBSOCKET_MAX_RECONNECT_ATTEMPTS = 5
            mock_config.WEBSOCKET_BASE_RECONNECT_DELAY = 1
            mock_config.WEBSOCKET_MAX_RECONNECT_DELAY = 10
            mock_config.WEBSOCKET_MAX_CONSECUTIVE_FAILURES = 3
            mock_config.WEBSOCKET_HEALTH_CHECK_INTERVAL = 30
            mock_config.WEBSOCKET_NIGHT_MODE_START_HOUR = 22
            mock_config.WEBSOCKET_NIGHT_MODE_END_HOUR = 6
            mock_config.WEBSOCKET_NIGHT_MODE_RECONNECT_INTERVAL = 60
            mock_config.WEBSOCKET_NO_MESSAGE_TIMEOUT = 300
            mock_config.WEBSOCKET_RECONNECT_COOLDOWN = 30

            client = WebSocketClient()
            return client

    def test_stability_initialization(self, ws_client):
        """测试稳定性机制初始化"""
        assert ws_client.no_message_timeout == 300
        assert ws_client.reconnect_cooldown == 30
        assert ws_client.last_successful_connect == 0
        assert ws_client.consecutive_failures == 0

    def test_connection_health_check_no_message_timeout(self, ws_client):
        """测试无消息超时的连接健康检查"""
        # 模拟WebSocket连接
        mock_ws = Mock()
        mock_ws.closed = False
        ws_client.ws = mock_ws

        # 设置长时间无消息
        ws_client.last_message_time = time.time() - 400  # 超过no_message_timeout(300)

        assert ws_client._is_connection_healthy() is False

    def test_connection_health_check_normal(self, ws_client):
        """测试正常情况下的连接健康检查"""
        # 模拟WebSocket连接
        mock_ws = Mock()
        mock_ws.closed = False
        ws_client.ws = mock_ws

        # 设置最近有消息
        ws_client.last_message_time = time.time() - 100  # 在no_message_timeout内

        assert ws_client._is_connection_healthy() is True

    @pytest.mark.asyncio
    async def test_send_pong(self, ws_client):
        """测试发送pong消息"""
        # 模拟WebSocket连接
        mock_ws = AsyncMock()
        mock_ws.closed = False
        ws_client.ws = mock_ws

        await ws_client._send_pong()

        mock_ws.send_str.assert_called_once_with("pong")

    @pytest.mark.asyncio
    async def test_send_pong_connection_closed(self, ws_client):
        """测试在连接关闭时发送pong"""
        # 模拟关闭的WebSocket连接
        mock_ws = Mock()
        mock_ws.closed = True
        ws_client.ws = mock_ws

        # 不应该抛出异常
        await ws_client._send_pong()

    def test_reset_reconnect_state(self, ws_client):
        """测试重置重连状态"""
        # 设置一些状态
        ws_client.consecutive_failures = 3
        ws_client.reconnect_attempts = 5
        ws_client.connection_healthy = False

        ws_client._reset_reconnect_state()

        # 验证状态被重置
        assert ws_client.consecutive_failures == 0
        assert ws_client.reconnect_attempts == 0
        assert ws_client.connection_healthy is True
        assert ws_client.last_successful_connect > 0

    @pytest.mark.asyncio
    async def test_handle_message_with_ping(self, ws_client):
        """测试处理ping消息"""
        # 模拟WebSocket连接
        mock_ws = AsyncMock()
        mock_ws.closed = False
        ws_client.ws = mock_ws

        # 模拟收到ping消息
        ping_message = '{"type": "ping", "timestamp": **********}'
        await ws_client._handle_message(ping_message)

        # 验证发送了pong响应
        mock_ws.send_str.assert_called_once_with("pong")

    @pytest.mark.asyncio
    async def test_safe_close_connection(self, ws_client):
        """测试安全关闭连接"""
        # 模拟WebSocket连接和会话
        mock_ws = AsyncMock()
        mock_session = AsyncMock()
        ws_client.ws = mock_ws
        ws_client.session = mock_session

        await ws_client._safe_close_connection()

        # 验证连接和会话被关闭
        mock_ws.close.assert_called_once()
        mock_session.close.assert_called_once()
        assert ws_client.ws is None
        assert ws_client.session is None
