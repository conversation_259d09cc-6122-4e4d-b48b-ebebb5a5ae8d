import json
import numpy as np
import cv2
import pytest

from app.api.detect import detect_image
from app.utils.config import config

@pytest.mark.asyncio
async def test_detect_image_base64_toggle(monkeypatch):
    # 构造一张简单图片
    img = np.zeros((10,10,3), dtype=np.uint8)
    _, buf = cv2.imencode('.jpg', img)

    # mock ImagePreprocessor.detect 返回包含 annotated_image 的结果
    class DummyProc:
        def detect(self, image, prompt, conf):
            return {"detections": [], "count": 0, "annotated_image": "abc"}
    monkeypatch.setattr('app.api.detect.ImagePreprocessor', lambda model_type: DummyProc())

    # 开启开关：应包含 annotated_image
    config.config_data['image_processing']['base64_output'] = True
    class AF:
        async def read(self):
            return buf.tobytes()
    resp = await detect_image(file=AF(), prompt='p', model_type='cv', confidence_threshold=0.5)
    data = json.loads(resp.body)
    assert 'annotated_image' in data

    # 关闭开关：不应包含 annotated_image
    config.config_data['image_processing']['base64_output'] = False
    resp2 = await detect_image(file=AF(), prompt='p', model_type='cv', confidence_threshold=0.5)
    data2 = json.loads(resp2.body)
    assert 'annotated_image' not in data2

