import pytest
from app.api.detect import reload_model
from starlette.datastructures import FormData
from fastapi import Request

class DummyScope(dict):
    def __init__(self):
        super().__init__()
        self.update({"type": "http", "method": "POST", "headers": []})

class DummyReceive:
    async def __call__(self):
        return {"type": "http.request", "body": b""}

class DummySend:
    async def __call__(self, message):
        pass

@pytest.mark.asyncio
async def test_reload_model_no_api_key(monkeypatch, tmp_path):
    # 创建一个假模型文件
    model_path = tmp_path / "dummy.pt"
    model_path.write_text("ok")

    # mock YOLOModel 初始化以通过验证
    class DummyModel:
        def __init__(self, p):
            pass
    monkeypatch.setattr('app.api.detect.YOLOModel', DummyModel)

    # 构造请求
    scope = DummyScope()
    request = Request(scope, DummyReceive())

    # 调用接口函数（直接调用函数签名）
    resp = await reload_model(request=request, model_path=str(model_path), ip_verified=True, rate_limit_checked=True)
    assert resp.status_code == 200

