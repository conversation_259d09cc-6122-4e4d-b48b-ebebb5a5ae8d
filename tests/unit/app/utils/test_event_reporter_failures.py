"""
EventReporter 失败/超时/部分成功的用例
"""
import os
import sys
import pytest
from unittest.mock import AsyncMock, patch
import logging

logger = logging.getLogger(__name__)

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
sys.path.insert(0, project_root)

from app.utils.event_reporter import EventReporter


@pytest.fixture
def event_reporter(mock_config, mock_file_manager):
    with patch('app.utils.event_reporter.config', mock_config), \
         patch('app.utils.event_reporter.file_manager', mock_file_manager):
        reporter = EventReporter()
        # 避免真实调用 DroneInfoManager
        reporter.drone_manager = AsyncMock()
        return reporter


@pytest.mark.asyncio
async def test_report_video_info_5xx_retry_then_success(event_reporter):
    """第一次 500，第二次 200，确认重试生效"""
    # 构造响应：第一次 500，第二次 200
    bad = AsyncMock(); bad.status = 500; bad.text = AsyncMock(return_value='err')
    ok = AsyncMock(); ok.status = 200; ok.text = AsyncMock(return_value='ok')

    # 模拟 session.post 的异步上下文
    ctx1 = AsyncMock(); ctx1.__aenter__.return_value = bad
    ctx2 = AsyncMock(); ctx2.__aenter__.return_value = ok

    mock_session = AsyncMock()
    mock_session.post.side_effect = [ctx1, ctx2]

    with patch('aiohttp.ClientSession', return_value=mock_session):
        await event_reporter._report_video_info('v.mp4', 'SN', 1609459200000)
        # 应调用两次 post
        assert mock_session.post.call_count == 2


@pytest.mark.asyncio
async def test_report_video_info_4xx_no_retry(event_reporter):
    """4xx 不应重试"""
    resp = AsyncMock(); resp.status = 400; resp.text = AsyncMock(return_value='bad')
    ctx = AsyncMock(); ctx.__aenter__.return_value = resp
    mock_session = AsyncMock(); mock_session.post.return_value = ctx

    with patch('aiohttp.ClientSession', return_value=mock_session):
        await event_reporter._report_video_info('v.mp4', 'SN', 1609459200000)
        assert mock_session.post.call_count == 1

