"""
EventReporter类的单元测试
"""
import pytest
import asyncio
from unittest.mock import MagicMock, patch, AsyncMock, call
import aiohttp
from datetime import datetime
import logging
import sys
import os

# 配置日志
logger = logging.getLogger(__name__)

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
sys.path.insert(0, project_root)

# 导入被测试的类
from app.utils.event_reporter import EventReporter


@pytest.fixture
def event_reporter(mock_config, mock_file_manager):
    """创建EventReporter实例的fixture"""
    logger.info("设置 event_reporter fixture")
    with patch('app.utils.event_reporter.config', mock_config), \
         patch('app.utils.event_reporter.file_manager', mock_file_manager):
        reporter = EventReporter()
        # 模拟DroneInfoManager
        reporter.drone_manager = MagicMock()
        drone_info = MagicMock()
        drone_info.drone_code = "TEST_DRONE"
        drone_info.longitude = 120.0
        drone_info.latitude = 30.0
        reporter.drone_manager.get_drone_info.return_value = drone_info
        logger.info("event_reporter fixture 设置完成")
        yield reporter
        logger.info("清理 event_reporter fixture")


@pytest.mark.asyncio
async def test_save_detection_video(event_reporter):
    """测试保存检测视频功能"""
    logger.info("开始测试 save_detection_video")
    # 模拟StreamProcessor
    stream_processor = MagicMock()
    stream_processor.drone_code = "TEST_DRONE"
    stream_processor.frame_rate = 25
    
    # 创建模拟帧
    mock_frame = MagicMock()
    mock_frame.frame = pytest.importorskip("numpy").zeros((720, 1280, 3), dtype='uint8')
    stream_processor.get_frames_around_timestamp.return_value = [mock_frame] * 10
    
    # 调用被测试方法
    timestamp = 1609459200000  # 2021-01-01 00:00:00
    logger.info(f"调用 save_detection_video，时间戳: {timestamp}")
    video_path = await event_reporter.save_detection_video(stream_processor, timestamp)
    
    # 验证结果
    logger.info(f"save_detection_video 返回路径: {video_path}")
    assert video_path == "/path/to/test_video.mp4"
    event_reporter.file_manager.save_video.assert_called_once()
    _args, kwargs = event_reporter.file_manager.save_video.call_args
    assert len(kwargs['frames']) == 10
    assert kwargs['frame_rate'] == 25
    logger.info("save_detection_video 测试完成")


@pytest.mark.asyncio
async def test_report_event_success(event_reporter):
    """测试成功上报事件"""
    logger.info("开始测试 report_event_success")
    # 模拟检测结果
    result = {
        "drone_code": "TEST_DRONE",
        "timestamp": 1609459200000,  # 2021-01-01 00:00:00
        "result": {
            "target_category": "船只",
            "detections": [
                {
                    "target_name": "船只",
                    "bbox": [100, 100, 200, 200],
                    "confidence": 0.95,
                    "class": "ship"
                }
            ],
            "error_code": "0",
            "image_paths": {
                "init": "/path/to/init_image.jpg",
                "res": "/path/to/res_image.jpg"
            }
        }
    }
    
    # 模拟StreamProcessor
    stream_processor = MagicMock()
    
    # 模拟save_detection_video方法，确保返回有效路径
    with patch.object(event_reporter, 'save_detection_video', new_callable=AsyncMock) as mock_save_video:
        mock_save_video.return_value = "/path/to/test_video.mp4"
        
        # 模拟HTTP响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text = AsyncMock(return_value='{"code": 0, "message": "success"}')
        
        # 模拟HTTP会话和响应
        with patch.object(event_reporter, '_get_session', new_callable=AsyncMock) as mock_get_session, \
             patch.object(event_reporter, '_post_cm_or_await', new_callable=AsyncMock) as mock_post, \
             patch('os.path.exists', return_value=True):

            # 设置模拟响应
            mock_post.return_value = mock_response

            # 调用被测试方法
            logger.info("调用 report_event 方法")
            result_success = await event_reporter.report_event(result, stream_processor)
            logger.info("report_event 方法调用完成")

            # 验证结果
            assert result_success is True, "事件上报应该成功"
            mock_save_video.assert_called_once_with(stream_processor, result["timestamp"])

            # 验证HTTP请求被调用了两次（事件上报 + 视频上报）
            assert mock_post.call_count == 2, f"应该调用2次HTTP请求，实际调用了{mock_post.call_count}次"
        
        logger.info("report_event_success 测试完成")


@pytest.mark.asyncio
async def test_report_event_no_drone_info(event_reporter):
    """测试无人机信息不存在的情况"""
    logger.info("开始测试 report_event_no_drone_info")
    # 模拟检测结果
    result = {
        "drone_code": "UNKNOWN_DRONE",
        "timestamp": 1609459200000,
        "result": {"target_category": "船只"}
    }
    
    # 模拟StreamProcessor
    stream_processor = MagicMock()
    
    # 设置drone_manager返回None
    event_reporter.drone_manager.get_drone_info.return_value = None
    
    # 调用被测试方法
    logger.info("调用 report_event 方法，使用未知无人机编码")
    await event_reporter.report_event(result, stream_processor)
    logger.info("report_event 方法调用完成")
    
    # 验证结果 - 应该提前返回，不调用save_detection_video
    event_reporter.file_manager.save_video.assert_not_called()
    logger.info("report_event_no_drone_info 测试完成")


@pytest.mark.asyncio
async def test_report_event_multiple_targets(event_reporter):
    """测试多目标检测的整合上报功能"""
    logger.info("开始测试 report_event_multiple_targets")
    # 模拟检测结果 - 包含多个不同类型的目标
    result = {
        "drone_code": "TEST_DRONE",
        "timestamp": 1609459200000,  # 2021-01-01 00:00:00
        "result": {
            "detections": [
                {
                    "target_name": "船只",
                    "bbox": [100, 100, 200, 200],
                    "confidence": 0.95,
                    "class": "ship"
                },
                {
                    "target_name": "疑似异常藻类",
                    "bbox": [300, 300, 400, 400],
                    "confidence": 0.85,
                    "class": "algae"
                },
                {
                    "target_name": "船只",  # 重复的目标类型
                    "bbox": [500, 500, 600, 600],
                    "confidence": 0.90,
                    "class": "ship"
                }
            ],
            "image_paths": {
                "init": "/path/to/init_image.jpg",
                "res": "/path/to/res_image.jpg"
            }
        }
    }

    # 模拟StreamProcessor
    stream_processor = MagicMock()

    # 模拟save_detection_video方法
    with patch.object(event_reporter, 'save_detection_video', new_callable=AsyncMock) as mock_save_video:
        mock_save_video.return_value = "/path/to/test_video.mp4"

        # 模拟HTTP响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text = AsyncMock(return_value='{"code": 0, "message": "success"}')

        # 模拟HTTP会话和响应
        with patch.object(event_reporter, '_get_session', new_callable=AsyncMock) as mock_get_session, \
             patch.object(event_reporter, '_post_cm_or_await', new_callable=AsyncMock) as mock_post, \
             patch('os.path.exists', return_value=True):

            # 设置模拟响应
            mock_post.return_value = mock_response

            # 调用被测试方法
            logger.info("调用 report_event 方法，测试多目标整合上报")
            result_success = await event_reporter.report_event(result, stream_processor)

            # 验证结果
            assert result_success is True, "多目标事件上报应该成功"

            # 验证只调用了2次HTTP请求（事件上报1次 + 视频上报1次，而不是3次事件上报）
            assert mock_post.call_count == 2, f"应该调用2次HTTP请求（事件+视频），实际调用了{mock_post.call_count}次"

            # 验证第一次调用是事件上报，第二次是视频上报
            first_call_args = mock_post.call_args_list[0]
            second_call_args = mock_post.call_args_list[1]

            # 第一次调用应该是事件上报URL
            assert first_call_args[0][1] == event_reporter.event_api_url, "第一次调用应该是事件上报接口"
            # 第二次调用应该是视频上报URL
            assert second_call_args[0][1] == event_reporter.video_api_url, "第二次调用应该是视频上报接口"

    logger.info("report_event_multiple_targets 测试完成")


@pytest.mark.asyncio
async def test_report_video_info(event_reporter):
    """测试上报视频信息功能"""
    logger.info("开始测试 report_video_info")
    # 模拟HTTP响应
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.text = AsyncMock(return_value='{"code": 0, "message": "success"}')
    
    # 模拟HTTP会话和响应
    with patch.object(event_reporter, '_get_session', new_callable=AsyncMock) as mock_get_session, \
         patch.object(event_reporter, '_post_cm_or_await', new_callable=AsyncMock) as mock_post, \
         patch('os.path.exists', return_value=True):

        # 设置模拟响应
        mock_post.return_value = mock_response

        # 调用被测试方法
        logger.info("调用 _report_video_info 方法")
        result = await event_reporter._report_video_info("test_video.mp4", "TEST_DRONE", 1609459200000)
        logger.info("_report_video_info 方法调用完成")

        # 验证结果
        assert result is True, "视频信息上报应该成功"
        assert mock_post.call_count == 1, f"应该调用1次HTTP请求，实际调用了{mock_post.call_count}次"
    logger.info("report_video_info 测试完成")