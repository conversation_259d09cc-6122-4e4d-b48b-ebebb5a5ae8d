"""
配置加载与关键属性的单元测试
"""
import os
import sys
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
sys.path.insert(0, project_root)

from app.utils.config import config


def test_config_basic_retention_and_temp_dirs():
    """验证保留策略与临时目录键名映射是否正确"""
    # storage.retention.days 默认 7（见 config.yaml）
    assert isinstance(config.RETENTION_DAYS, int)
    assert config.RETENTION_DAYS >= 0
    assert config.CLEANUP_TIME  # 非空

    # 临时基础目录按 config.yaml: storage.temp.base
    assert config.TEMP_BASE_DIR.endswith('app_data/temp')


def test_video_stream_rtmp_base_url_present():
    """验证 RTMP 基础 URL 可从统一路径读取"""
    base_url = config.video_stream.rtmp.base_url
    assert isinstance(base_url, str)
    assert base_url.startswith('rtmp://') or base_url.startswith('webrtc://')


def test_security_disabled_placeholders():
    """API 安全相关字段已停用，应返回占位默认值"""
    assert config.API_KEY == ""
    assert config.ALLOWED_IPS == []
    assert config.API_RATE_LIMIT_MAX_REQUESTS == 0
    assert config.API_RATE_LIMIT_TIME_WINDOW == 0

