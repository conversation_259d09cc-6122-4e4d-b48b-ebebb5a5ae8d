import threading
import time
import numpy as np
import pytest

from app.utils.thread_frame_queue import ThreadFrameQueue
from app.utils.frame_data import FrameData


def make_frame(fid: int) -> FrameData:
    return FrameData(
        frame=np.zeros((10, 10, 3), dtype=np.uint8),
        timestamp=time.time() * 1000,
        frame_id=fid,
        metadata={}
    )


class TestThreadFrameQueue:
    def test_put_get_basic(self):
        q = ThreadFrameQueue(max_size=2, drop_old_frames=False)
        assert q.size() == 0
        assert q.get(timeout=0.01) is None

        assert q.put(make_frame(1)) is True
        assert q.put(make_frame(2)) is True
        # 满了且不丢旧帧
        assert q.put(make_frame(3)) is False
        f = q.get(timeout=0.1)
        assert f is not None and f.frame_id == 1

    def test_drop_old_frames(self):
        q = ThreadFrameQueue(max_size=2, drop_old_frames=True)
        q.put(make_frame(1))
        q.put(make_frame(2))
        # 触发丢旧帧
        q.put(make_frame(3))
        assert q.size() == 2
        f = q.get(timeout=0.1)
        assert f is not None and f.frame_id == 2
        f = q.get(timeout=0.1)
        assert f is not None and f.frame_id == 3

    def test_blocking_get(self):
        q = ThreadFrameQueue(max_size=2, drop_old_frames=True)

        def producer():
            time.sleep(0.05)
            q.put(make_frame(7))

        t = threading.Thread(target=producer)
        t.start()
        f = q.get(timeout=0.2)
        t.join()
        assert f is not None and f.frame_id == 7

    def test_close(self):
        q = ThreadFrameQueue(max_size=1, drop_old_frames=False)
        q.put(make_frame(1))
        q.close()
        assert q.get(timeout=0.01) is None
        assert q.put(make_frame(2)) is False

