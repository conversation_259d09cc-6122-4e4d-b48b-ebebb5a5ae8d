import os
import time
from app.utils.file_manager import file_manager


def test_file_manager_fallback_creates_temp_dir(tmp_path, monkeypatch):
    # 模拟NFS目录创建失败
    def mock_makedirs_fail(path, exist_ok):
        raise OSError("Simulated NFS failure")

    # 替换 os.makedirs 仅在第一次调用（创建NFS日期目录）时抛错
    calls = {"count": 0}
    real_makedirs = os.makedirs

    def makedirs_side_effect(path, exist_ok=True):
        if calls["count"] == 0:
            calls["count"] += 1
            raise OSError("Simulated NFS failure")
        return real_makedirs(path, exist_ok)

    monkeypatch.setattr(os, "makedirs", makedirs_side_effect)

    # 替换临时目录为 pytest 提供的临时路径
    monkeypatch.setattr(file_manager, "temp_dirs", {
        'videos': str(tmp_path / 'videos'),
        'reports': str(tmp_path / 'reports'),
        'images': str(tmp_path / 'images'),
    })

    ts = int(time.time() * 1000)
    save_path = file_manager.get_save_path('images', 'a.jpg', ts)

    assert save_path is not None
    # 路径应该落在临时目录
    assert str(tmp_path) in save_path

