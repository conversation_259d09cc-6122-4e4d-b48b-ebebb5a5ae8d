import numpy as np
import pytest

from app.processor.image_processor import ImagePreprocessor


def test_sr_lazy_init(monkeypatch, tmp_path):
    # 强制依赖不可用
    monkeypatch.setattr('app.processor.image_processor.RRDBNet', None, raising=False)
    monkeypatch.setattr('app.processor.image_processor.RealESRGANer', None, raising=False)

    # 避免加载真实YOLO权重
    class DummyYOLO:
        def __init__(self, p):
            self.model = type('M', (), {'model_name': 'dummy'})
    monkeypatch.setattr('app.models.yolo_model.YOLOModel', DummyYOLO)

    p = ImagePreprocessor()
    # 初始不应创建 upsampler
    assert getattr(p, 'upsampler', None) is None

    # 输入一张图，调用超分
    img = np.zeros((16,16,3), dtype=np.uint8)
    out = p._super_resolution(img)
    # 依赖不可用时回退为原图
    assert out.shape == img.shape

