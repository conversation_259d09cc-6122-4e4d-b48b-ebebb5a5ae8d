import pytest
import asyncio
import threading
import time
import cv2
import numpy as np
from unittest.mock import MagicMock, patch

# Add project root to Python path
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../..')))

from app.video.streams.rtmp_connection_manager import RTMPConnectionManager
from app.video.streams.frame_producer import FrameProducer
from app.utils.async_frame_queue import AsyncFrameQueue
from app.utils.config import config

class TestRtmpThreadSafety:
    """
    Test the thread safety of RTMPConnectionManager and FrameProducer
    """

    @pytest.fixture
    def mock_video_capture(self):
        """Fixture to mock cv2.VideoCapture."""
        mock_cap = MagicMock(spec=cv2.VideoCapture)
        mock_cap.isOpened.return_value = True
        
        # Simulate frame reading
        def read_side_effect(*args, **kwargs):
            # Simulate a slight delay
            time.sleep(0.01)
            # Return a valid frame
            return True, np.zeros((480, 640, 3), dtype=np.uint8)
        
        mock_cap.read.side_effect = read_side_effect
        mock_cap.release.return_value = None
        return mock_cap

    @patch('cv2.VideoCapture')
    def test_concurrent_read_and_reconnect(self, mock_video_capture_class, mock_video_capture):
        """
        Simulates concurrent reading from one thread and reconnecting from another.
        This test ensures thread-safe operations on the connection manager.
        """
        mock_video_capture_class.return_value = mock_video_capture
        
        # Setup
        rtmp_url = "rtmp://fake/stream"
        connection_manager = RTMPConnectionManager(rtmp_url)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # Initial connection must be run in the loop
        connection_successful = loop.run_until_complete(connection_manager.create_connection())
        assert connection_successful

        stop_event = threading.Event()
        errors = []

        # Thread 1: The Reader (simulates FrameProducer)
        def reader_thread():
            while not stop_event.is_set():
                try:
                    ret, frame = connection_manager.read_frame()
                    # In this test, it's okay if read fails during reconnect
                except Exception as e:
                    errors.append(f"Reader thread error: {e}")
                    break
                time.sleep(0.005)

        # Thread 2: The Reconnector (schedules tasks on the main loop)
        def reconnector_thread():
            reconnect_count = 0
            while not stop_event.is_set() and reconnect_count < 5:
                try:
                    # Schedule the reconnect coroutine on the main event loop
                    future = asyncio.run_coroutine_threadsafe(connection_manager.reconnect(), loop)
                    # Wait for the result
                    future.result(timeout=2.0)
                    reconnect_count += 1
                except Exception as e:
                    # We expect timeouts here if the main loop is busy, which is fine
                    if isinstance(e, asyncio.TimeoutError):
                        pass
                    else:
                        errors.append(f"Reconnector thread error: {e}")
                        break
                time.sleep(0.02)
        
        # Main thread runner
        async def main_runner():
            reader = threading.Thread(target=reader_thread)
            reconnector = threading.Thread(target=reconnector_thread)

            reader.start()
            reconnector.start()

            # Run the event loop for a duration
            await asyncio.sleep(0.5)

            # Stop the threads
            stop_event.set()
            reader.join(timeout=1)
            reconnector.join(timeout=1)

            # Assertions
            assert not errors, f"Errors occurred during concurrent access: {errors}"
            assert not reader.is_alive(), "Reader thread did not terminate correctly."
            assert not reconnector.is_alive(), "Reconnector thread did not terminate correctly."

        # Run the main async test runner
        loop.run_until_complete(main_runner())
        loop.close()
        
        # Final verification
        assert mock_video_capture.release.call_count >= 5
        assert mock_video_capture.read.call_count > 10

if __name__ == "__main__":
    pytest.main([__file__]) 