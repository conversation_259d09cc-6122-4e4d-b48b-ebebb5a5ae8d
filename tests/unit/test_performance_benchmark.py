#!/usr/bin/env python3
"""
性能基准测试工具的单元测试

测试内容：
- ResourceMonitor类的功能
- PerformanceBenchmark类的基本功能
- 报告生成功能
- 错误处理
"""

import os
import sys
import json
import tempfile
import unittest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import numpy as np
import cv2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tools.performance_benchmark import ResourceMonitor, PerformanceBenchmark


class TestResourceMonitor(unittest.TestCase):
    """ResourceMonitor类的测试"""
    
    def setUp(self):
        """测试前准备"""
        self.monitor = ResourceMonitor()
        
    def tearDown(self):
        """测试后清理"""
        if self.monitor.monitoring:
            self.monitor.stop_monitoring()
            
    def test_init(self):
        """测试初始化"""
        self.assertFalse(self.monitor.monitoring)
        self.assertIsNone(self.monitor.monitor_thread)
        self.assertEqual(len(self.monitor.cpu_usage), 0)
        self.assertEqual(len(self.monitor.memory_usage), 0)
        
    @patch('psutil.cpu_percent')
    @patch('psutil.virtual_memory')
    def test_start_stop_monitoring(self, mock_memory, mock_cpu):
        """测试开始和停止监控"""
        # 模拟系统资源数据
        mock_cpu.return_value = 50.0
        mock_memory.return_value = Mock(used=1024*1024*1024)  # 1GB
        
        # 开始监控
        self.monitor.start_monitoring()
        self.assertTrue(self.monitor.monitoring)
        self.assertIsNotNone(self.monitor.monitor_thread)
        
        # 等待一小段时间让监控收集数据
        import time
        time.sleep(0.2)
        
        # 停止监控
        self.monitor.stop_monitoring()
        self.assertFalse(self.monitor.monitoring)
        
        # 检查是否收集到数据
        self.assertGreater(len(self.monitor.cpu_usage), 0)
        self.assertGreater(len(self.monitor.memory_usage), 0)
        
    def test_get_stats_empty(self):
        """测试空数据的统计信息"""
        stats = self.monitor.get_stats()
        
        self.assertIn('cpu', stats)
        self.assertIn('memory', stats)
        self.assertEqual(stats['cpu']['avg'], 0)
        self.assertEqual(stats['memory']['avg'], 0)
        
    def test_get_stats_with_data(self):
        """测试有数据的统计信息"""
        # 添加测试数据
        self.monitor.cpu_usage = [10.0, 20.0, 30.0]
        self.monitor.memory_usage = [100.0, 200.0, 300.0]

        stats = self.monitor.get_stats()

        self.assertEqual(stats['cpu']['avg'], 20.0)
        self.assertEqual(stats['cpu']['max'], 30.0)
        self.assertEqual(stats['cpu']['min'], 10.0)
        self.assertEqual(stats['memory']['avg'], 200.0)

    def test_baseline_snapshot(self):
        """测试基线快照功能"""
        self.monitor.take_baseline_snapshot()

        self.assertIsNotNone(self.monitor.baseline_snapshot)
        self.assertIn('timestamp', self.monitor.baseline_snapshot)
        self.assertIn('cpu_percent', self.monitor.baseline_snapshot)
        self.assertIn('memory_used_mb', self.monitor.baseline_snapshot)

    def test_model_loaded_snapshot(self):
        """测试模型加载后快照功能"""
        self.monitor.take_model_loaded_snapshot()

        self.assertIsNotNone(self.monitor.model_loaded_snapshot)
        self.assertIn('timestamp', self.monitor.model_loaded_snapshot)
        self.assertIn('cpu_percent', self.monitor.model_loaded_snapshot)
        self.assertIn('memory_used_mb', self.monitor.model_loaded_snapshot)

    def test_incremental_analysis(self):
        """测试增量分析功能"""
        # 设置模拟的基线和模型加载后数据
        self.monitor.baseline_snapshot = {
            'cpu_percent': 10.0,
            'memory_used_mb': 1000.0,
            'gpu_utilization_percent': 0.0,
            'gpu_memory_used_mb': 100.0,
            'torch_gpu_memory_allocated_mb': 0.0
        }

        self.monitor.model_loaded_snapshot = {
            'cpu_percent': 15.0,
            'memory_used_mb': 1500.0,
            'gpu_utilization_percent': 5.0,
            'gpu_memory_used_mb': 600.0,
            'torch_gpu_memory_allocated_mb': 500.0
        }

        # 添加推理过程数据
        self.monitor.memory_usage = [1600.0, 1650.0, 1700.0]
        self.monitor.gpu_memory_usage = [650.0, 700.0, 750.0]

        analysis = self.monitor.get_incremental_analysis()

        # 检查分析结果结构
        self.assertIn('baseline', analysis)
        self.assertIn('model_loaded', analysis)
        self.assertIn('model_loading_increment', analysis)
        self.assertIn('inference_increment', analysis)
        self.assertIn('total_increment', analysis)

        # 检查模型加载增量计算
        model_inc = analysis['model_loading_increment']
        self.assertEqual(model_inc['memory_mb_increase'], 500.0)  # 1500 - 1000
        self.assertEqual(model_inc['gpu_memory_mb_increase'], 500.0)  # 600 - 100
        self.assertEqual(model_inc['torch_gpu_memory_mb_increase'], 500.0)  # 500 - 0


class TestPerformanceBenchmark(unittest.TestCase):
    """PerformanceBenchmark类的测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录和文件
        self.temp_dir = tempfile.mkdtemp()
        self.temp_model_path = os.path.join(self.temp_dir, "test_model.pt")
        self.temp_images_dir = os.path.join(self.temp_dir, "images")
        
        # 创建临时模型文件（空文件）
        with open(self.temp_model_path, 'w') as f:
            f.write("dummy model file")
            
        # 创建图片目录
        os.makedirs(self.temp_images_dir, exist_ok=True)
        
        # 创建测试图片
        self.create_test_images()
        
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def create_test_images(self):
        """创建测试图片"""
        # 创建几张测试图片
        for i in range(3):
            # 创建随机图片
            image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            image_path = os.path.join(self.temp_images_dir, f"test_image_{i}.jpg")
            cv2.imwrite(image_path, image)
            
    def test_init_success(self):
        """测试成功初始化"""
        benchmark = PerformanceBenchmark(
            model_path=self.temp_model_path,
            test_images_dir=self.temp_images_dir,
            max_images=10
        )
        
        self.assertEqual(benchmark.model_path, self.temp_model_path)
        self.assertEqual(benchmark.test_images_dir, self.temp_images_dir)
        self.assertEqual(benchmark.max_images, 10)
        
    def test_init_model_not_found(self):
        """测试模型文件不存在的情况"""
        with self.assertRaises(FileNotFoundError):
            PerformanceBenchmark(
                model_path="/nonexistent/model.pt",
                test_images_dir=self.temp_images_dir
            )
            
    def test_init_images_dir_not_found(self):
        """测试图片目录不存在的情况"""
        with self.assertRaises(FileNotFoundError):
            PerformanceBenchmark(
                model_path=self.temp_model_path,
                test_images_dir="/nonexistent/images"
            )
            
    def test_load_test_images(self):
        """测试加载测试图片"""
        benchmark = PerformanceBenchmark(
            model_path=self.temp_model_path,
            test_images_dir=self.temp_images_dir,
            max_images=10
        )
        
        benchmark.load_test_images()
        
        # 检查是否加载了图片
        self.assertGreater(len(benchmark.test_images), 0)
        self.assertLessEqual(len(benchmark.test_images), 3)  # 我们创建了3张图片
        
        # 检查图片数据结构
        for image_info in benchmark.test_images:
            self.assertIn('path', image_info)
            self.assertIn('data', image_info)
            self.assertIn('name', image_info)
            self.assertIsInstance(image_info['data'], np.ndarray)
            
    def test_load_test_images_with_limit(self):
        """测试限制图片数量"""
        benchmark = PerformanceBenchmark(
            model_path=self.temp_model_path,
            test_images_dir=self.temp_images_dir,
            max_images=2  # 限制为2张
        )
        
        benchmark.load_test_images()
        
        # 检查是否正确限制了数量
        self.assertLessEqual(len(benchmark.test_images), 2)
        
    @patch('tools.performance_benchmark.YOLO')
    def test_benchmark_device_cpu(self, mock_ultra_yolo):
        """测试CPU模式基准测试"""
        # 模拟Ultralytics YOLO实例为可调用对象
        mock_model_instance = MagicMock()
        mock_model_instance.return_value = Mock()  # 模拟推理结果
        mock_ultra_yolo.return_value = mock_model_instance

        benchmark = PerformanceBenchmark(
            model_path=self.temp_model_path,
            test_images_dir=self.temp_images_dir,
            max_images=2
        )
        
        benchmark.load_test_images()
        
        # 运行CPU基准测试
        result = benchmark.benchmark_device('cpu')
        
        # 检查结果结构
        self.assertIn('device', result)
        self.assertIn('total_time', result)
        self.assertIn('successful_inferences', result)
        self.assertIn('inference_times', result)
        self.assertIn('throughput', result)
        self.assertIn('resource_usage', result)
        self.assertIn('incremental_analysis', result)

        self.assertEqual(result['device'], 'cpu')
        
    @patch('torch.cuda.is_available')
    def test_benchmark_device_cuda_not_available(self, mock_cuda_available):
        """测试CUDA不可用时的GPU测试"""
        mock_cuda_available.return_value = False

        benchmark = PerformanceBenchmark(
            model_path=self.temp_model_path,
            test_images_dir=self.temp_images_dir
        )

        # 使用日志捕获来验证警告消息
        with self.assertLogs('tools.performance_benchmark', level='WARNING') as log:
            result = benchmark.benchmark_device('cuda')

        # 应该返回空字典
        self.assertEqual(result, {})
        # 验证警告消息
        self.assertIn('CUDA不可用，跳过GPU测试', log.output[0])
        
    def test_generate_comparison(self):
        """测试生成对比分析"""
        benchmark = PerformanceBenchmark(
            model_path=self.temp_model_path,
            test_images_dir=self.temp_images_dir
        )
        
        # 创建模拟的CPU和GPU结果
        cpu_result = {
            'inference_times': {'avg': 0.1},  # 100ms
            'throughput': {'images_per_second': 10.0},
            'resource_usage': {
                'cpu': {'avg': 50.0},
                'memory': {'avg': 1000.0}
            }
        }
        
        gpu_result = {
            'inference_times': {'avg': 0.05},  # 50ms
            'throughput': {'images_per_second': 20.0},
            'resource_usage': {
                'cpu': {'avg': 30.0},
                'memory': {'avg': 1200.0},
                'gpu': {'avg': 80.0},
                'gpu_memory': {'avg': 2000.0}
            }
        }
        
        comparison = benchmark._generate_comparison(cpu_result, gpu_result)
        
        # 检查对比结果
        self.assertIn('inference_speedup', comparison)
        self.assertIn('throughput_improvement', comparison)
        self.assertIn('resource_usage', comparison)
        
        # 检查加速倍数计算
        speedup = comparison['inference_speedup']['speedup_factor']
        self.assertAlmostEqual(speedup, 2.0, places=1)  # 100ms / 50ms = 2x
        
    def test_generate_report_without_results(self):
        """测试没有结果时生成报告"""
        benchmark = PerformanceBenchmark(
            model_path=self.temp_model_path,
            test_images_dir=self.temp_images_dir
        )
        
        with self.assertRaises(ValueError):
            benchmark.generate_report()
            
    def test_save_results_json_without_results(self):
        """测试没有结果时保存JSON"""
        benchmark = PerformanceBenchmark(
            model_path=self.temp_model_path,
            test_images_dir=self.temp_images_dir
        )
        
        with self.assertRaises(ValueError):
            benchmark.save_results_json("test.json")
            
    def test_generate_report_with_mock_results(self):
        """测试使用模拟结果生成报告"""
        benchmark = PerformanceBenchmark(
            model_path=self.temp_model_path,
            test_images_dir=self.temp_images_dir
        )
        
        # 设置模拟结果
        benchmark.results = {
            'system_info': {
                'cpu_count': 8,
                'memory_total': 16.0,
                'cuda_available': True,
                'cuda_device_count': 1,
                'cuda_device_name': 'Test GPU',
                'model_path': self.temp_model_path,
                'test_images_count': 10,
                'timestamp': '2024-01-01T12:00:00'
            },
            'benchmarks': {
                'cpu': {
                    'total_time': 10.0,
                    'successful_inferences': 10,
                    'failed_inferences': 0,
                    'inference_times': {
                        'avg': 0.1, 'max': 0.15, 'min': 0.08,
                        'median': 0.1, 'std': 0.02, 'total': 1.0
                    },
                    'throughput': {
                        'images_per_second': 1.0,
                        'ms_per_image': 100.0
                    },
                    'resource_usage': {
                        'cpu': {'avg': 50.0, 'max': 60.0},
                        'memory': {'avg': 1000.0, 'max': 1100.0}
                    }
                }
            }
        }
        
        # 生成报告
        report = benchmark.generate_report()
        
        # 检查报告内容
        self.assertIn("模型性能基准测试报告", report)
        self.assertIn("CPU核心数: 8", report)
        self.assertIn("CPU 模式测试结果", report)
        self.assertIn("平均: 100.00 ms", report)
        
    def test_save_results_json_with_mock_results(self):
        """测试保存JSON结果"""
        benchmark = PerformanceBenchmark(
            model_path=self.temp_model_path,
            test_images_dir=self.temp_images_dir
        )
        
        # 设置模拟结果
        test_results = {'test': 'data'}
        benchmark.results = test_results
        
        # 保存到临时文件
        json_file = os.path.join(self.temp_dir, "test_results.json")
        benchmark.save_results_json(json_file)
        
        # 验证文件是否正确保存
        self.assertTrue(os.path.exists(json_file))
        
        with open(json_file, 'r', encoding='utf-8') as f:
            loaded_results = json.load(f)
            
        self.assertEqual(loaded_results, test_results)


if __name__ == '__main__':
    unittest.main()
