#!/usr/bin/env python
"""
专门运行event_reporter.py测试的脚本
"""
import os
import sys
import pytest
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, root_dir)
logger.info(f"项目根目录: {root_dir}")

# 确保使用config_fake.yaml作为配置文件
os.environ['CONFIG_FILE'] = 'config.yaml'
logger.info(f"使用配置文件: {os.environ['CONFIG_FILE']}")

def run_event_reporter_tests():
    """运行event_reporter.py的测试"""
    logger.info("运行event_reporter.py的测试...")
    
    # 获取测试目录的完整路径
    test_dir = os.path.dirname(os.path.abspath(__file__))
    unit_dir = os.path.join(test_dir, 'unit')
    
    # 获取event_reporter测试文件的路径
    event_reporter_test_path = os.path.join(unit_dir, 'app', 'utils', 'test_event_reporter.py')
    
    if not os.path.exists(event_reporter_test_path):
        logger.error(f"未找到event_reporter测试文件: {event_reporter_test_path}")
        return 1
    
    logger.info(f"找到event_reporter测试文件: {event_reporter_test_path}")
    
    # 运行event_reporter测试
    return pytest.main(["-xvs", event_reporter_test_path])

def run_specific_test(test_name):
    """运行特定的测试函数"""
    logger.info(f"运行特定测试: {test_name}...")
    
    # 获取测试目录的完整路径
    test_dir = os.path.dirname(os.path.abspath(__file__))
    unit_dir = os.path.join(test_dir, 'unit')
    
    # 获取event_reporter测试文件的路径
    event_reporter_test_path = os.path.join(unit_dir, 'app', 'utils', 'test_event_reporter.py')
    
    if not os.path.exists(event_reporter_test_path):
        logger.error(f"未找到event_reporter测试文件: {event_reporter_test_path}")
        return 1
    
    # 运行特定测试
    return pytest.main(["-xvs", f"{event_reporter_test_path}::{test_name}"])

if __name__ == "__main__":
    import argparse
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='运行event_reporter.py的测试')
    parser.add_argument('--test', help='指定要运行的测试函数名称')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    if args.test:
        # 运行特定测试
        result = run_specific_test(args.test)
    else:
        # 运行所有event_reporter测试
        result = run_event_reporter_tests()
    
    # 退出程序，返回测试结果
    sys.exit(result) 