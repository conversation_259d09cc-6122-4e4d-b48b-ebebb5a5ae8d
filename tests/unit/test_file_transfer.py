#!/usr/bin/env python3
"""
文件转移功能测试脚本
"""

import os
import sys
import asyncio
import tempfile
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.file_transfer_manager import file_transfer_manager
from app.utils.config import config

def create_test_files():
    """创建测试文件"""
    # 创建临时测试目录
    test_temp_dir = "./test_temp"
    test_mount_dir = "./test_mount"
    
    os.makedirs(test_temp_dir, exist_ok=True)
    os.makedirs(test_mount_dir, exist_ok=True)
    
    # 创建日期目录
    today = datetime.now().strftime('%Y%m%d')
    temp_date_dir = os.path.join(test_temp_dir, today)
    mount_date_dir = os.path.join(test_mount_dir, today)
    
    os.makedirs(temp_date_dir, exist_ok=True)
    os.makedirs(mount_date_dir, exist_ok=True)
    
    # 创建测试文件
    test_files = [
        "drone001_20250114120000.jpg",
        "drone001_20250114120001.jpg",
        "drone002_20250114120000.jpg",
        "drone002_20250114120001.jpg"
    ]
    
    for filename in test_files:
        file_path = os.path.join(temp_date_dir, filename)
        with open(file_path, 'w') as f:
            f.write(f"Test content for {filename}")
    
    print(f"创建了 {len(test_files)} 个测试文件在 {temp_date_dir}")
    return test_temp_dir, test_mount_dir, today

def test_file_transfer():
    """测试文件转移功能"""
    print("开始测试文件转移功能...")
    
    # 创建测试文件
    temp_dir, mount_dir, date_str = create_test_files()
    
    try:
        # 临时修改配置以使用测试目录
        original_temp_dirs = file_transfer_manager.temp_dirs.copy()
        original_mount_points = file_transfer_manager.mount_points.copy()
        
        file_transfer_manager.temp_dirs['images'] = temp_dir
        file_transfer_manager.mount_points['images'] = mount_dir
        
        # 执行文件转移
        results = file_transfer_manager.transfer_files_by_date('images', date_str)
        
        print(f"文件转移结果: {results}")
        
        # 验证结果
        if results['success'] > 0:
            print("✅ 文件转移测试成功")
        else:
            print("❌ 文件转移测试失败")
            
        # 检查文件是否已转移
        mount_date_dir = os.path.join(mount_dir, date_str)
        if os.path.exists(mount_date_dir):
            files_in_mount = os.listdir(mount_date_dir)
            print(f"挂载目录中的文件: {files_in_mount}")
        
    finally:
        # 恢复原始配置
        file_transfer_manager.temp_dirs = original_temp_dirs
        file_transfer_manager.mount_points = original_mount_points
        
        # 清理测试目录
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        if os.path.exists(mount_dir):
            shutil.rmtree(mount_dir)

async def test_async_file_transfer():
    """测试异步文件转移功能"""
    print("开始测试异步文件转移功能...")
    
    # 创建测试文件
    temp_dir, mount_dir, date_str = create_test_files()
    
    try:
        # 临时修改配置以使用测试目录
        original_temp_dirs = file_transfer_manager.temp_dirs.copy()
        original_mount_points = file_transfer_manager.mount_points.copy()
        
        file_transfer_manager.temp_dirs['images'] = temp_dir
        file_transfer_manager.mount_points['images'] = mount_dir
        
        # 执行异步文件转移
        results = await file_transfer_manager.transfer_drone_files_async("drone001")
        
        print(f"异步文件转移结果: {results}")
        
        # 验证结果
        if any(stats['success'] > 0 for stats in results.values()):
            print("✅ 异步文件转移测试成功")
        else:
            print("❌ 异步文件转移测试失败")
            
    finally:
        # 恢复原始配置
        file_transfer_manager.temp_dirs = original_temp_dirs
        file_transfer_manager.mount_points = original_mount_points
        
        # 清理测试目录
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        if os.path.exists(mount_dir):
            shutil.rmtree(mount_dir)

def main():
    """主函数"""
    print("文件转移功能测试")
    print("=" * 50)
    
    # 测试同步文件转移
    test_file_transfer()
    
    print("\n" + "=" * 50)
    
    # 测试异步文件转移
    asyncio.run(test_async_file_transfer())
    
    print("\n测试完成")

if __name__ == "__main__":
    main() 