"""
初始化测试，确保测试框架能够正确运行
"""
import pytest
import logging
import os

# 配置日志
logger = logging.getLogger(__name__)

def test_environment():
    """测试环境是否正确设置"""
    logger.info("运行基本环境测试")
    
    # 检查配置文件环境变量
    assert os.environ.get('CONFIG_FILE') == 'config.yaml', "配置文件环境变量设置错误"
    
    # 检查项目根目录是否存在
    root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
    assert os.path.exists(root_dir), "项目根目录不存在"
    
    # 检查app目录是否存在
    app_dir = os.path.join(root_dir, 'app')
    assert os.path.exists(app_dir), "app目录不存在"
    
    logger.info("基本环境测试通过") 