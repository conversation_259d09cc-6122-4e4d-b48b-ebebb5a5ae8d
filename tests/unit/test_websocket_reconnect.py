#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WebSocket重连功能测试脚本
用于验证改造后的WebSocket重连机制是否正常工作
"""

import asyncio
import logging
import sys
import time
from datetime import datetime

# 添加项目路径到sys.path
sys.path.append('.')

from app.database.websocket_client import WebSocketClient
from app.utils.config import config
from app.utils.shared_state import shared_state_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("websocket_test")

class WebSocketReconnectTest:
    """WebSocket重连测试类"""
    
    def __init__(self):
        """初始化测试"""
        self.ws_client = WebSocketClient()
        self.test_duration = 300  # 测试持续时间（秒）
        self.start_time = None
        
    async def test_connection(self):
        """测试连接功能"""
        logger.info("开始测试WebSocket连接功能...")
        
        # 测试获取token
        logger.info("测试获取token...")
        if await self.ws_client.get_token():
            logger.info("✅ Token获取成功")
        else:
            logger.error("❌ Token获取失败")
            return False
        
        # 测试建立连接
        logger.info("测试建立WebSocket连接...")
        if await self.ws_client.connect():
            logger.info("✅ WebSocket连接建立成功")
        else:
            logger.error("❌ WebSocket连接建立失败")
            return False
        
        return True
    
    async def test_reconnect_logic(self):
        """测试重连逻辑"""
        logger.info("开始测试重连逻辑...")
        
        # 测试重连延迟计算
        logger.info("测试重连延迟计算...")
        for i in range(5):
            self.ws_client.reconnect_attempts = i
            delay = self.ws_client._calculate_reconnect_delay()
            logger.info(f"第 {i+1} 次重连延迟: {delay:.1f} 秒")
        
        # 测试夜间模式判断
        logger.info("测试夜间模式判断...")
        is_night = self.ws_client._is_night_mode()
        logger.info(f"当前是否为夜间模式: {is_night}")
        
        # 测试重连尝试判断
        logger.info("测试重连尝试判断...")
        self.ws_client.reconnect_attempts = 0  # 重置重连次数
        should_reconnect = self.ws_client._should_attempt_reconnect()
        logger.info(f"是否应该尝试重连: {should_reconnect}")
        
        # 测试达到最大重连次数的情况
        self.ws_client.reconnect_attempts = self.ws_client.max_reconnect_attempts
        should_reconnect = self.ws_client._should_attempt_reconnect()
        logger.info(f"达到最大重连次数后是否应该重连: {should_reconnect}")
        
        return True
    
    async def test_shared_state(self):
        """测试共享状态管理"""
        logger.info("开始测试共享状态管理...")
        
        # 设置状态文件路径 - 使用绝对路径
        import os
        status_file = os.path.join(os.getcwd(), "test_drone_status.json")
        shared_state_manager.set_status_file_path(status_file)
        logger.info(f"状态文件路径: {status_file}")
        
        # 测试更新无人机状态
        test_drone = "TEST_DRONE_001"
        test_status = {
            'device_sn': test_drone,
            'mode_code': 4,
            'is_active': True,
            'last_update': time.time()
        }
        
        shared_state_manager.update_drone_status(test_drone, test_status)
        logger.info("✅ 无人机状态更新成功")
        
        # 测试获取活跃无人机
        active_drones = shared_state_manager.get_active_drones()
        logger.info(f"当前活跃无人机: {list(active_drones.keys())}")
        
        # 测试状态摘要
        summary = shared_state_manager.get_status_summary()
        logger.info(f"状态摘要: {summary}")
        
        # 测试手动保存状态
        if shared_state_manager.save_status_manually():
            logger.info("✅ 状态保存成功")
        else:
            logger.error("❌ 状态保存失败")
        
        # 测试清理过期状态
        shared_state_manager.cleanup_expired_status()
        logger.info("✅ 过期状态清理完成")
        
        return True
    
    async def test_config_integration(self):
        """测试配置集成"""
        logger.info("开始测试配置集成...")
        
        # 测试WebSocket重连配置
        logger.info(f"最大重连尝试次数: {config.WEBSOCKET_MAX_RECONNECT_ATTEMPTS}")
        logger.info(f"基础重连延迟: {config.WEBSOCKET_BASE_RECONNECT_DELAY} 秒")
        logger.info(f"最大重连延迟: {config.WEBSOCKET_MAX_RECONNECT_DELAY} 秒")
        logger.info(f"最大连续失败次数: {config.WEBSOCKET_MAX_CONSECUTIVE_FAILURES}")
        logger.info(f"健康检查间隔: {config.WEBSOCKET_HEALTH_CHECK_INTERVAL} 秒")
        
        # 测试夜间模式配置
        logger.info(f"夜间模式开始时间: {config.WEBSOCKET_NIGHT_MODE_START_HOUR}:00")
        logger.info(f"夜间模式结束时间: {config.WEBSOCKET_NIGHT_MODE_END_HOUR}:00")
        logger.info(f"夜间重连间隔: {config.WEBSOCKET_NIGHT_MODE_RECONNECT_INTERVAL} 秒")
        
        return True
    
    async def run_tests(self):
        """运行所有测试"""
        logger.info("=" * 50)
        logger.info("开始WebSocket重连功能测试")
        logger.info("=" * 50)
        
        self.start_time = time.time()
        
        try:
            # 测试配置集成
            await self.test_config_integration()
            
            # 测试共享状态管理
            await self.test_shared_state()
            
            # 测试重连逻辑
            await self.test_reconnect_logic()
            
            # 测试连接功能（可选，需要真实的WebSocket服务器）
            # await self.test_connection()
            
            logger.info("=" * 50)
            logger.info("✅ 所有测试完成")
            logger.info("=" * 50)
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {str(e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
        
        finally:
            # 清理测试文件
            import os
            if os.path.exists("test_drone_status.json"):
                os.remove("test_drone_status.json")
                logger.info("清理测试文件完成")

async def main():
    """主函数"""
    test = WebSocketReconnectTest()
    await test.run_tests()

if __name__ == "__main__":
    asyncio.run(main()) 