#!/usr/bin/env python3
"""
测试活跃无人机检测功能修复
"""

import sys
import os
import time
import asyncio
import logging

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils.config import config
from app.utils.shared_state import shared_state_manager, active_drones
from app.database.drone_info import DroneInfoManager
from app.database.websocket_client import WebSocketClient

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_shared_state_manager():
    """测试共享状态管理器"""
    logger.info("=== 测试共享状态管理器 ===")
    
    # 清空现有状态
    active_drones.clear()
    
    # 模拟添加一些无人机状态
    test_drones = [
        {
            'device_sn': 'drone001',
            'mode_code': 4,  # 活跃模式
            'is_active': True,
            'last_update': time.time()
        },
        {
            'device_sn': 'drone002', 
            'mode_code': 1,  # 非活跃模式
            'is_active': False,
            'last_update': time.time()
        },
        {
            'device_sn': 'drone003',
            'mode_code': 3,  # 活跃模式
            'is_active': True,
            'last_update': time.time()
        }
    ]
    
    # 添加测试数据
    for drone_status in test_drones:
        device_sn = drone_status['device_sn']
        shared_state_manager.update_drone_status(device_sn, drone_status)
        logger.info(f"添加测试无人机: {device_sn}")
    
    # 测试获取活跃无人机（不应用配置过滤）
    logger.info("\n--- 测试不应用配置过滤 ---")
    active_drones_no_filter = shared_state_manager.get_active_drones(apply_config_filter=False)
    active_ids_no_filter = shared_state_manager.get_active_drone_ids(apply_config_filter=False)
    logger.info(f"不应用配置过滤的活跃无人机: {list(active_drones_no_filter.keys())}")
    logger.info(f"不应用配置过滤的活跃无人机ID: {active_ids_no_filter}")
    
    # 测试获取活跃无人机（应用配置过滤）
    logger.info("\n--- 测试应用配置过滤 ---")
    active_drones_with_filter = shared_state_manager.get_active_drones(apply_config_filter=True)
    active_ids_with_filter = shared_state_manager.get_active_drone_ids(apply_config_filter=True)
    logger.info(f"应用配置过滤的活跃无人机: {list(active_drones_with_filter.keys())}")
    logger.info(f"应用配置过滤的活跃无人机ID: {active_ids_with_filter}")
    logger.info(f"配置文件中的无人机列表: {config.DRONE_LIST}")
    logger.info(f"只监控配置无人机设置: {config.stream_monitor.only_monitor_config_drones}")
    
    # 测试活跃状态统计
    logger.info("\n--- 测试活跃状态统计 ---")
    summary = shared_state_manager.get_activity_summary()
    logger.info(f"活跃状态统计: {summary}")
    
    return len(active_ids_no_filter) > 0

def test_drone_info_manager():
    """测试无人机信息管理器"""
    logger.info("\n=== 测试无人机信息管理器 ===")
    
    try:
        drone_manager = DroneInfoManager()
        active_drones_list = drone_manager.get_active_drones()
        logger.info(f"DroneInfoManager 获取的活跃无人机: {active_drones_list}")
        return True
    except Exception as e:
        logger.error(f"测试 DroneInfoManager 失败: {str(e)}")
        return False

def test_websocket_client():
    """测试 WebSocket 客户端"""
    logger.info("\n=== 测试 WebSocket 客户端 ===")
    
    try:
        ws_client = WebSocketClient()
        active_drones_list = ws_client.get_active_drone_ids()
        logger.info(f"WebSocketClient 获取的活跃无人机: {active_drones_list}")
        
        # 测试单个无人机活跃状态检查
        if active_drones_list:
            test_drone = active_drones_list[0]
            is_active = ws_client.is_drone_active(test_drone)
            logger.info(f"无人机 {test_drone} 是否活跃: {is_active}")
        
        return True
    except Exception as e:
        logger.error(f"测试 WebSocketClient 失败: {str(e)}")
        return False

def test_config_filtering():
    """测试配置过滤逻辑"""
    logger.info("\n=== 测试配置过滤逻辑 ===")
    
    # 显示当前配置
    logger.info(f"配置的无人机列表: {config.DRONE_LIST}")
    logger.info(f"只监控配置无人机: {config.stream_monitor.only_monitor_config_drones}")
    logger.info(f"活跃模式代码: {config.WEBSOCKET_ACTIVE_MODE_CODES}")
    
    # 测试不同的配置过滤场景
    logger.info("\n--- 场景1: 不应用配置过滤 ---")
    result1 = shared_state_manager.get_active_drone_ids(apply_config_filter=False)
    logger.info(f"结果: {result1}")
    
    logger.info("\n--- 场景2: 应用配置过滤 ---")
    result2 = shared_state_manager.get_active_drone_ids(apply_config_filter=True)
    logger.info(f"结果: {result2}")
    
    return True

def main():
    """主测试函数"""
    logger.info("开始测试活跃无人机检测功能修复")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("共享状态管理器", test_shared_state_manager()))
    test_results.append(("无人机信息管理器", test_drone_info_manager()))
    test_results.append(("WebSocket客户端", test_websocket_client()))
    test_results.append(("配置过滤逻辑", test_config_filtering()))
    
    # 输出测试结果
    logger.info("\n=== 测试结果汇总 ===")
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 所有测试通过！活跃无人机检测功能修复成功。")
    else:
        logger.error("\n❌ 部分测试失败，需要进一步检查。")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
