# NFS文件同步工具配置文件

# 时间筛选配置
time_filter:
  # 启用时间筛选 (true/false)
  enabled: true
  
  # 开始时间 (格式: YYYY-MM-DD HH:MM:SS)
  start_time: "2025-06-30 00:00:00"
  
  # 结束时间 (格式: YYYY-MM-DD HH:MM:SS)
  # 如果为空或null，则使用当前时间
  end_time: "2025-09-15 10:00:00"

# 同步配置
sync:
  # 远程服务器路径
  remote_path: "D:\\droneData\\imageFile"
  
  # 本地存储路径
  local_path: "app_data/DroneData/imageFile"
  
  # 支持的文件扩展名
  supported_extensions:
    - ".jpg"
    - ".jpeg" 
    - ".png"
    - ".bmp"
    - ".tiff"
    - ".gif"
  
  # 是否启用断点续传
  resume_download: true
  
  # 文件大小检查 (跳过已存在且大小相同的文件)
  check_file_size: true
  
  # 文件修改时间检查 (跳过本地文件更新的情况)
  check_modification_time: true

# 网络连接配置
network:
  # 连接超时时间 (秒)
  timeout: 30
  
  # 重试次数
  retry_count: 3
  
  # 重试间隔 (秒)
  retry_interval: 5

# Linux系统挂载配置
mount:
  # 可能的挂载点路径 (按优先级排序)
  possible_paths:
    - "/mnt/{server}_droneData/imageFile"
    - "/media/{server}/droneData/imageFile"
    - "/tmp/nfs_mount/droneData/imageFile"
  
  # 临时挂载点
  temp_mount_point: "/tmp/nfs_sync_mount"
  
  # SMB挂载选项
  smb_options: "guest,ro,iocharset=utf8"

# 日志配置
logging:
  # 日志级别: DEBUG, INFO, WARNING, ERROR
  level: "INFO"
  
  # 是否显示详细进度
  show_progress: true
  
  # 是否显示文件传输统计
  show_stats: true

# 交互模式配置
interactive:
  # 是否启用交互模式 (false时使用配置文件设置)
  enabled: false
  
  # 同步全部文件时是否需要确认
  confirm_sync_all: true 