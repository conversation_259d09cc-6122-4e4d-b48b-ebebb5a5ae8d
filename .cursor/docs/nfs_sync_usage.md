# NFS文件同步工具使用说明

## 概述

`nfs_sync_simple.py` 是一个简单的NFS文件同步工具，用于从远程NFS服务器同步图片文件到本地目录。

## 功能特性

- **时间筛选**：支持按文件修改时间筛选需要同步的文件
- **断点续传**：支持中断后继续传输，避免重复下载
- **进度显示**：实时显示文件传输进度
- **智能跳过**：自动跳过已存在且完整的文件

## 使用方法

### 1. 直接运行

在项目根目录下，直接运行：

```bash
python tools/nfs_sync_simple.py
```

### 2. 在IDE中运行

在IDE中打开 `tools/nfs_sync_simple.py` 文件，直接运行即可。

## 配置要求

工具需要两个配置文件：

### 1. 主配置文件 (`config_fake.yaml`)
从项目根目录读取NFS服务器信息：
```yaml
storage:
  nfs:
    server: "xxx.xxx.xxx.xxx"  # NFS服务器IP地址
```

### 2. 同步配置文件 (`config/nfs_sync_config.yaml`)
包含同步工具的详细配置，主要配置项：

```yaml
# 时间筛选配置
time_filter:
  enabled: true                    # 启用时间筛选
  start_time: "2024-01-01 00:00:00"  # 开始时间
  end_time: null                   # 结束时间（null表示当前时间）

# 交互模式配置  
interactive:
  enabled: false         # 禁用交互模式，使用配置文件设置
  confirm_sync_all: true # 同步全部文件时是否确认
```

## 同步设置

- **远程路径**：`D:\droneData\imageFile`（Windows NFS服务器路径）
- **本地路径**：`app_data/DroneData/imageFile`（本地存储路径）
- **支持的文件格式**：`.jpg`, `.jpeg`, `.png`, `.bmp`, `.tiff`, `.gif`

## 时间筛选选项

### 配置文件模式（推荐）
通过修改 `config/nfs_sync_config.yaml` 设置时间范围：

- **start_time: "2024-01-01 00:00:00"** + **end_time: null** - 同步2024年1月1日至今的文件
- **start_time: "2024-01-01 08:00:00"** + **end_time: "2024-01-01 18:00:00"** - 同步指定时间段的文件
- **start_time: null** + **end_time: null** - 同步所有文件（禁用时间筛选）
- **start_time: "2024-01-01 00:00:00"** + **end_time: "2024-01-31 23:59:59"** - 同步整个1月的文件

**时间格式说明**：
- 格式：`YYYY-MM-DD HH:MM:SS`
- `end_time` 为 `null` 时使用当前时间
- `start_time` 为 `null` 时不限制开始时间

### 交互模式
如果设置 `interactive.enabled: true`，运行时会提示选择：

1. **最近24小时** - 同步最近一天的文件
2. **最近3天** - 同步最近三天的文件
3. **最近一周** - 同步最近一周的文件
4. **最近一个月** - 同步最近一个月的文件
5. **全部文件** - 同步所有文件（需要确认）
6. **自定义日期** - 输入特定日期（格式：YYYY-MM-DD）

## 断点续传机制

- 工具会检查本地文件的完整性
- 如果本地文件大小小于远程文件，会从断点继续下载
- 如果本地文件已完整，会自动跳过

## 注意事项

1. **网络连接**：确保能够访问NFS服务器
2. **权限要求**：确保有读取远程文件的权限（Linux系统可能需要sudo权限进行挂载）
3. **磁盘空间**：确保本地有足够的存储空间
4. **中断恢复**：如果传输中断，重新运行工具即可继续
5. **系统兼容性**：支持Windows和Linux系统，Linux系统会自动尝试SMB挂载

## 错误处理

- 网络连接失败会自动重试
- 单个文件传输失败不会影响其他文件
- 所有错误信息都会记录在日志中

## 依赖包

工具依赖以下Python包：
- `pyyaml` - 配置文件解析
- `tqdm` - 进度条显示
- `pathlib` - 路径操作

这些依赖应该已经包含在项目的 `requirements.txt` 中。

## 故障排除

### Linux系统常见问题

1. **挂载失败**：如果提示权限不足，请确保当前用户有sudo权限
2. **CIFS工具缺失**：如果提示找不到CIFS，请安装：
   ```bash
   # Ubuntu/Debian
   sudo apt-get install cifs-utils
   
   # CentOS/RHEL
   sudo yum install cifs-utils
   ```

3. **网络不通**：检查防火墙设置和SMB端口（445）是否开放

### Windows系统常见问题

1. **UNC路径访问失败**：确保网络发现已开启
2. **权限问题**：可能需要在网络凭据中添加服务器认证信息

### 通用问题

1. **配置文件错误**：检查 `config_fake.yaml` 中的服务器IP是否正确
2. **文件格式问题**：工具只同步图片文件，其他格式会被忽略
3. **磁盘空间不足**：检查目标目录所在磁盘的可用空间 