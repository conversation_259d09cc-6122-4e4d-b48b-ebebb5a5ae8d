# 程序阻塞问题解决指南

## 问题描述

程序在运行过程中突然停止所有日志输出，但进程仍然存在，无法通过正常方式停止，最终需要使用 `kill` 命令强制终止。

## 根本原因分析

经过深入分析，确定问题的根本原因是 **WebSocket消息循环缺乏超时控制导致阻塞**：

### 技术细节
- 在 `app/database/websocket_client.py` 第434行的 `async for msg in self.ws:` 循环
- 当WebSocket连接进入异常状态（既不发送消息也不正确关闭）时
- 该循环会无限期等待，阻塞整个事件循环
- 导致所有定时任务和日志输出停止

### 问题表现
1. 程序进程存在但完全"静默"
2. 所有定时任务停止执行
3. 所有日志输出停止
4. 程序无法响应正常停止信号

## 解决方案

### 1. 核心修复 - WebSocket超时控制 ✅

**文件**: `app/database/websocket_client.py`

为WebSocket消息循环添加30秒超时控制：

```python
# 连接成功，处理消息
try:
    async with asyncio.timeout(30):  # 30秒超时
        async for msg in self.ws:
            if msg.type == aiohttp.WSMsgType.TEXT:
                await self._handle_message(msg.data)
            elif msg.type == aiohttp.WSMsgType.CLOSED:
                break
            elif msg.type == aiohttp.WSMsgType.ERROR:
                break
except asyncio.TimeoutError:
    logger.warning("WebSocket消息循环超时，强制断开连接")
    if self.ws and not self.ws.closed:
        await self.ws.close()
except Exception as e:
    logger.error(f"WebSocket消息处理异常: {str(e)}")
    if self.ws and not self.ws.closed:
        await self.ws.close()
```

### 2. 监控服务增强 ✅

**文件**: `app/services/monitor_service.py`

添加了心跳日志和异常恢复机制：

- 每2分钟输出心跳日志，确保主循环正常运行
- 增强异常处理和自动恢复功能
- 自动重启异常退出的关键组件

### 3. 流监控器保护 ✅

**文件**: `app/processor/stream_processor.py`

为流监控器添加：

- 每3分钟的心跳日志
- 30秒超时控制
- 单个无人机处理的异常隔离

### 4. 配置优化 ✅

**文件**: `config_fake.yaml`

优化了关键配置参数：

```yaml
scheduler:
  reconnect_interval: 120  # 2分钟
  status_check_interval: 30  # 30秒

websocket:
  reconnect:
    max_delay: 120  # 降低最大延迟
    max_consecutive_failures: 5  # 增加容错性
    message_timeout: 30  # 新增消息超时
    connection_timeout: 15  # 新增连接超时
```

## 使用方式

继续使用原有的启动方式：

```bash
# 启动程序
nohup python main.py > nohup.out 2>&1 &

# 查看日志
tail -f nohup.out

# 停止程序（如果需要）
ps aux | grep "python main.py" | grep -v grep | awk '{print $2}' | xargs kill
```

## 问题诊断

如果程序再次出现阻塞，可以通过以下方式确认：

1. **检查日志活动**：
   ```bash
   # 查看最后修改时间
   ls -la nohup.out
   
   # 查看最新日志
   tail -20 nohup.out
   ```

2. **查找心跳日志**：
   现在程序会定期输出心跳日志，如果没有心跳日志说明可能再次阻塞

3. **检查进程状态**：
   ```bash
   ps aux | grep "python main.py"
   ```

## 预期效果

通过以上核心修复：

1. **WebSocket超时控制**：防止消息循环无限期阻塞
2. **心跳监控**：可以明确知道程序是否正常运行
3. **异常恢复**：关键组件异常时自动重启
4. **超时保护**：各个处理环节都有超时保护

这些修复应该能彻底解决程序阻塞问题，同时保持原有的简单启动方式。

## 测试建议

重新启动程序后，观察以下几点：

1. 心跳日志是否正常出现（每2-3分钟）
2. 定时任务是否持续执行
3. WebSocket连接异常时是否能正常恢复
4. 程序是否能正常响应停止信号

如果以上都正常，说明问题已经解决。 