## 背景
- 现场日志在 10:00:00 执行“夜间模式检查”后停止输出，随后 Ctrl+C 无响应，只能 kill -9 终止
- 目标：定位“卡住”的原因，不求修复方案

## 关键线索（来自代码检视）
1) APScheduler 任务定义（app/services/monitor_service.py -> setup_scheduler）
- 定时任务包含：重连、状态检查、WebSocket健康检查、夜间模式检查、NFS自检与重挂载、关键统计日志
- 调度器为 AsyncIOScheduler，任务函数多为 async def

2) 可疑阻塞点 A：在异步任务中调用同步阻塞
- 夜间模式检查本身逻辑简单，不阻塞
- 但同一调度器中另一个任务“_check_nfs_and_remount”会在协程内调用同步函数 nfs_manager.check_and_remount()
- nfs_manager.check_and_remount() 内部可能触发 _mount_nfs()，其中大量使用 subprocess.run(...) 与 time.sleep(...)（同步阻塞）
- 若该阻塞发生在事件循环线程，将导致：
  - 事件循环停顿 -> 后续调度与日志都不再执行
  - 信号处理（Ctrl+C）仅安排了协程任务，无法被调度执行，看起来“无响应”

3) 可疑阻塞点 B：其他同步调用
- 关键统计任务 _log_key_stats 调用 nfs_manager.get_status()（内部 _is_mounted 使用 subprocess.run(['mount']))，虽通常很快，但在系统异常时也可能阻塞

4) 非主要嫌疑
- WebSocketClient._process_messages 使用 async for 读取消息，不会阻塞整个事件循环（仅阻塞自身任务）
- StreamProcessor 的 time.sleep() 发生在独立线程，不会阻塞事件循环

## 触发时序推测
- APScheduler 在 10:00:00 触发“夜间模式检查”并记录 executed successfully
- 同时刻或随后，另一个“间隔”型任务（如 NFS 自检或关键统计）开始执行并在协程体内调用同步阻塞逻辑
- 从而导致：
  - Scheduler 不再“wake up”；
  - 根日志器也没有机会输出（因为 loop 被同步阻塞拖住）；
  - Ctrl+C 信号到达后，AppState 仅能创建优雅关闭任务，但该任务无法被调度执行

## 我已添加的最小化“诊断日志”（不改变业务行为）
- app/services/monitor_service.py
  - _check_nfs_and_remount：记录起止与耗时
  - _log_key_stats：细分 NFS 状态、流状态、组件统计各自耗时
- app/utils/nfs_manager.py
  - check_and_remount：记录总耗时以及单个挂载动作耗时

目的：一旦再次复现，即可从日志中判定是否为 NFS 路径阻塞，以及具体阻塞在哪一步

## 我新增的诊断测试（可选）
- tests/diagnostic/test_event_loop_blocking_by_sync_calls.py
  - 用 time.sleep 模拟在 async APScheduler 任务中执行同步阻塞，验证事件循环会被拖住（作为现象佐证）
  - 另一个用 monkeypatch 将 nfs_manager.check_and_remount 设为阻塞，验证该路径会拖住事件循环（作为路径佐证）
- 说明：若本机没有 pytest，可暂不运行；这两个测试的价值在于“行为复制”与“路径定位”，不依赖现场环境

## 建议的复现与采集步骤
1) 正常启动程序，等待出现“夜间模式检查”附近的触发点
2) 一旦卡住，检查最新日志是否包含如下诊断行：
   - “[诊断] 开始执行 NFS 自检与重挂载任务”
   - “[诊断] NFS 自检任务耗时: X ms, 结果=... ”
   - “[诊断] 关键统计采集耗时: NFS=...ms, 流状态=...ms, 组件=...ms”
   - “[诊断] NFS check_and_remount 总耗时: ... ms, success=...”
3) 结论判定：
   - 若上述“[诊断]”日志成对出现，且耗时显著（秒级），基本可判定为该路径同步阻塞事件循环
   - 若“[诊断]”日志缺失，则说明卡住发生在其他路径（后续可对其他定时任务逐一加同类耗时日志）

## 当前定位结论（阶段性）
- 最有可能的根因：协程任务内部调用了同步阻塞逻辑（以 NFS 自检/重挂载路径为首要嫌疑），使事件循环停顿，连带导致 Ctrl+C 无法触发优雅关闭
- 需要一次现场复现配合新增诊断日志来确认

## 下一步计划（仅定位，不修复）
1) 请按上节步骤运行并收集一次“卡住”前后 2-3 分钟的日志（包含“[诊断]”关键字）
2) 如果“[诊断]”日志显示 NFS 路径耗时异常：
   - 记录具体是哪个子步骤阻塞（mount / systemctl / rpcinfo / ...）
3) 如果“[诊断]”日志无异常：
   - 我将对其它 APScheduler 任务加同等粒度的耗时日志，并对 WebSocketClient 关键路径加进出埋点，以排除其它可疑点

（备注：本文档仅用于排查记录；一旦确认根因，再制定改造方案）



## 2025-08-18 新增证据与阶段性结论

- 测试结果
  - test_monitor_nfs_job_blocking_effect：将 nfs_manager.check_and_remount 打桩为 time.sleep(1.2) 的同步阻塞，结果总耗时 elapsed≈1.201s，断言失败（期望 <0.3s）。这从侧面证明：一旦在“异步任务”中调用了同步阻塞，会实质拖住事件循环，符合现场“日志停滞 + Ctrl+C 无响应”的表现。
  - 辅助测试（在异步任务中直接 time.sleep）亦可复制同类现象。

- 本次复现的运行日志（关键片段）
  - 10:44:05：NFS 自检与重挂载、关键统计均显示毫秒级耗时，正常返回；Scheduler 随后继续运行
  - 10:44:35：Scheduler 仍活跃，状态检查/WebSocket健康检查均成功
  - 10:44:36 之后至 11:00:48：无日志输出（静默期）
  - 11:00:48：日志恢复（含心跳与活跃无人机检查等）

- 对上述日志的解读
  1) 在进入静默前，NFS 相关任务耗时毫秒级，无法证明“该次静默由 NFS 路径直接触发”。因此，不能简单归因于 10:44:05 的 NFS 任务。
  2) 静默开始前的最后调度提示为 Next wakeup at 10:45:00，这与“夜间模式检查（每5分钟）”时间点重合，但夜间模式检查逻辑本身极轻（仅计算小时并设置 flag），从代码上看不构成阻塞。
  3) 除 NFS/夜间模式外，仍存在其它潜在阻塞源（见下），有必要对它们同样进行耗时埋点以收集证据。

- 目前可以初步“局部排除”的嫌疑
  - 10:44:05 触发的那一轮 NFS 自检/重挂载 与 关键统计：本轮证据表明它们未造成阻塞（耗时毫秒级）。

- 仍需重点排查的可疑路径（待加同级别耗时埋点）
  1) 重新初始化视频流任务：MonitorService._reinit_drone_streams（每 <=60 秒）
     - 该任务会关闭/初始化各视频流，内部 await 多处操作；一旦某处引入同步阻塞（或底层封装偶发耗时），可能拖住事件循环。
  2) 活跃无人机状态检查：MonitorService.log_active_drones
     - 虽然逻辑看似轻量，但使用 threading.Lock 的共享状态访问若在异步上下文里遇到竞争，理论上可能阻塞（需验证是否有跨线程写入竞争）。
  3) WebSocketClient 关键路径
     - start/_process_messages/消息处理回调链条（含 _forward_drone_data->DroneInfoManager.handle_websocket_data->shared_state 更新）。
  4) 其它 APScheduler 任务（_check_websocket_health、_check_night_mode）
     - 理论上轻量，但为排除偶发因素，建议同样加进/出与耗时埋点。

- 是否移除当前新增的诊断日志？
  - 暂不移除。现有“[诊断]”日志已有效排除 NFS/关键统计在本轮静默前的直接阻塞；但根因尚未查清，保留这些埋点可继续提供高价值证据。

- 下一步定位计划（仍不做功能性改造，仅加/调试日志）
  1) 为以下任务与关键函数增加与现有相同风格的耗时日志（进入/退出/耗时毫秒）：
     - MonitorService._reinit_drone_streams
     - MonitorService.log_active_drones（含获取 active ids 的子步骤）
     - MonitorService._check_websocket_health / _check_night_mode（轻量确认）
     - WebSocketClient._process_messages 主循环入口/退出、每批消息处理批次的周期性心跳（避免刷屏）
  2) 一旦再次出现静默，依据“最后一条[诊断]日志”精确锁定最后成功完成的任务，然后对紧邻的下一个计划任务重点跟踪。

- 小结
  - “协程中同步阻塞会卡住事件循环”已被测试与经验充分印证
  - 本次静默并非 10:44:05 的 NFS/关键统计直接触发，真正的阻塞源仍未锁定
  - 建议按上面的下一步计划继续加掩码级耗时埋点，以在下一次复现时收敛到具体函数/调用点


## 2025-08-18 新证据：线程堆栈与明确卡点路径

- 触发前日志（关键）：
  - [诊断] 调度器事件: code=4096（Job executed）针对多个任务；随后 WebSocket 心跳继续
- StallWatchdog 输出的线程堆栈（节选）：
  - 来自 app/database/drone_info.py 路径：handle_websocket_data -> _update_drone_status -> get_drone_info -> _load_drone_info -> _get_db_connection
  - 在 mysql.connector.connection.is_connected / cmd_ping / network.recv_plain 处阻塞

- 结论（定位）：
  - 在 WebSocket 消息处理的异步上下文中，调用了同步 MySQL 操作（is_connected/cmd_ping，及后续 cursor.execute/fetchone）。
  - 这些同步数据库操作会阻塞事件循环线程，解释了“调度器无 wakeup、日志停滞、Ctrl+C 无响应”。

- 新增诊断埋点（仅定位、无功能改动）：
  - app/database/drone_info.py
    - _get_db_connection：记录 is_connected 的耗时与结果、首次 connect 的耗时、总耗时
    - _load_drone_info：记录 get_conn、execute、fetchone 各阶段耗时
  - app/database/websocket_client.py
    - _handle_message：进入/退出耗时；_process_drone_status 与注册 handler 的耗时
  - app/services/monitor_service.py
    - 已有：_event_loop_watchdog 与 APScheduler 事件监听

- 下一步采集建议：
  1) 继续运行并等待静默重现；一旦出现，立即抓取“静默前后5分钟”的日志。
  2) 重点关注：
     - [诊断][DB] is_connected 耗时: xxx ms, ok=...
     - [诊断][DB] 连接建立耗时: xxx ms
     - [诊断][DB] _load_drone_info 耗时: get_conn/execute/fetchone 分段
     - [诊断] _handle_message 进入/退出与内部子步骤耗时
     - [诊断] 事件循环延迟异常 drift
     - StallWatchdog 线程堆栈
  3) 如果静默期间多次出现 is_connected 耗时显著升高或长时间没有对应“退出 _handle_message”，基本可坐实“DB 同步调用卡住事件循环”的根因。

- 现阶段暂不移除任何诊断日志：
  - 它们对收敛定位仍有高价值。一旦根因完全确认，再统一回收。


## 2025-08-19 阶段结论（预加载方案有效）

- 现状：在将“无人机信息在初始化阶段一次性预加载到内存，运行期不再访问数据库”的方案上线后，至今未再复现“卡住/无日志”问题。
- 结论：初步判定根因（异步上下文内同步DB调用阻塞事件循环）已被规避；预加载方案有效。
- 建议：继续运行观察1–2天；若仍无复现，我将进一步回收剩余诊断日志（保留极少量关键生命周期与异常告警）。
