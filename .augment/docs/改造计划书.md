# 无人机视频识别系统改造计划书（v1）

## 背景与目标
- 背景：现有系统已具备多路无人机视频接入、实时检测、事件上报与NFS备份的能力，但在配置一致性、稳定性、观测性与性能等方面存在优化空间。
- 总体目标：在不改变对外功能的前提下，提升系统稳定性、可维护性与性能吞吐；确保未来易扩展（新增视频源/模型/上报方式）。

## 关键约束与您的调整
- 移除 API_KEY 相关设计与校验，暂不需要。
- 配置系统不做文件拆分；优先简化与强化校验，减少臃肿，避免重复/冲突键名。
- 弃用项先“注释弃用”，待单元测试与手工集成验证后再删除。
- 健康检查与观测性简化：仅提供基础健康检查与关键指标，不引入复杂依赖。

## 现状评审摘要（精简）
- 配置：app/utils/config.py 存在重复属性、键名不一致（如 storage.temp.base vs base_dir）、仍在使用已弃用 config.rtmp。
- 流处理：线程内频繁 run_coroutine_threadsafe 与主事件循环交互，潜在卡顿与积压风险；缺少检测并发限流与背压。
- 事件上报：每次新建 aiohttp.ClientSession；缺乏重试/退避与速率限制。
- **WebSocket连接**：30秒硬超时导致频繁断开，复杂的ping/pong机制不适配服务器，影响无人机状态识别。[已解决]
- NFS：依赖 sudo/平台差异，建议降级策略，避免阻断主流程。
- 观测性：仅文本日志，缺少基础健康探针与关键指标暴露。

## 设计与改造方案

### 1. 配置系统精简与强校验（不拆分文件）
- 去重与一致性：移除重复属性，统一键名（storage.temp.base），规范访问路径；将 config.rtmp 标记为“注释弃用”，统一改为 config.video_stream.rtmp.base_url。
- 强校验：引入 pydantic 验证（仅用于加载/校验阶段，不拆文件），确保必填项、枚举/范围、路径存在性等；加载失败时给出明确日志并终止。
- 文档化：在 README 与本计划书同步更新关键配置说明，减少隐式规则。

取舍：不拆文件，改为在单文件中使用结构化模型+校验，减少行数增殖与重复定义。

### 2. 流处理稳态化与限流
- 同步帧接口：为视频流对象提供线程友好的 get_frame_blocking（内部由 async 队列桥接），避免 run_coroutine_threadsafe 的频繁跨 loop 调用。
- 检测并发控制：引入 asyncio.Semaphore 控制并发检测任务数；为检测任务设置超时与熔断（基于连续失败计数）。
- 背压策略：当检测队列饱和时丢弃旧帧或跳帧（可配置），保证系统在压力下保持稳定。

### 3. 事件上报稳定性
- Session 复用：在 EventReporter 生命周期内复用 aiohttp.ClientSession。
- 重试与退避：对可重试错误（网络异常/5xx）使用指数退避（上限与总时长可配置），4xx 不重试。
- 速率限制：加入轻量令牌桶或最小间隔发送，避免雪崩；支持并发上限。
- 日志最小化：仅记录请求关键信息与聚合结果，避免泄露敏感与过量日志。

### 4. NFS 可选化与自愈
- 尝试挂载失败时不阻断主流程，回落到本地存储并给出告警日志。
- 提供“检查并重挂载”定时任务（简化频率），以及手工触发接口；控制 mount/umount 超时。

### 5. 简化健康检查与观测性
- /healthz：返回关键组件健康（WebSocket 连接、活跃处理器数量、NFS 挂载状态）。
- 轻量级指标：在日志中周期性打印核心统计（检测QPS、上报成功率、连续读取失败计数）；暂不引入 Prometheus 等重型依赖。

### 6. 性能细节
- 流式路径默认不返回 Base64 标注图，仅保存图片并返回路径；API 场景可通过参数开启。
- Real-ESRGAN 延迟初始化；仅在启用时加载并显式选择设备与 FP16。

### 7. 安全与弃用管理
- 移除 API_KEY 相关代码与文档。
- 弃用项先注释（如 config.rtmp、旧 Detector 入口等），待测试与验收后删除。

## 任务拆解与里程碑

里程碑 M1（配置与上报基础，稳定优先）
1. 配置清理与强校验（不拆文件） [已完成：键名对齐、占位停用安全字段、基础单测]
2. 统一使用 config.video_stream.rtmp.base_url；注释弃用 config.rtmp [已完成：RTMPStream 引用更新、弃用注释]
3. EventReporter：Session 复用 + 重试/退避 + 限流 [已完成：会话复用、轻量重试、最小间隔限流]
4. 单元测试：配置加载、上报成功/失败/超时、部分成功 [已补充：配置加载 & 上报失败用例；成功/部分成功将在集成测试体现]
5. **WebSocket稳定性改进**：修复频繁断开问题，彻底重构连接管理 [已完成：移除复杂心跳，简化消息循环，优化重连逻辑]
6. **视频流稳定性改进**：解决空帧导致流停止问题，区分空帧和真正失败 [已完成：重构帧读取逻辑，增加容错性]
7. **流检查逻辑优化**：修复重启后无法重连问题，增加启动缓冲期 [已完成：优化check_stream逻辑，增加初始化等待]

里程碑 M2（流处理稳态与限流）
5. get_frame_blocking 与检测并发 Semaphore；任务超时与熔断
6. 背压策略（丢旧帧/跳帧）与参数化
7. 回归测试：test_stream_integration、压力测试基线

里程碑 M3（NFS 与健康检查简化）
8. NFS 降级与自愈（不阻断主流程，定期自检）
9. /healthz 与周期性关键统计日志
10. 文档与运维指引更新

里程碑 M4（性能与清理）
11. Base64 标注图开关；Real-ESRGAN 按需加载
12. 注释弃用项清理（在验收测试通过后删除）
13. 最终回归与稳定性测试

里程碑 M5（清理与发布准备）
14. 移除 API_KEY 相关校验与文档引用（按“关键约束”落地）
15. 清理已注释的弃用项与旧配置键（如 config.rtmp），仅保留统一访问路径
16. README/配置样例与运维指引最终修订（仅保留必要开关与参数说明）
17. 发布前回归检查与版本标记（不引入新依赖）

## 测试策略
- 单元测试：配置、事件上报（含重试/限流）、流处理并发/背压、小型基准。
- 集成测试：WebSocket 模拟、RTMP/帧注入、NFS 挂载可选场景。
- 压力测试：固定输入帧率下的检测吞吐与延迟曲线，观察降级行为。

## 风险与回滚
- 风险：线程-异步改造引入新并发问题；NFS 权限/平台差异。
- 回滚：按里程碑小步提交；每步保持功能旗标与可切换路径；回归测试先行。

## 输出物
- 改动代码、迁移文档（本计划书）与测试用例（tests 目录）。
- README 与配置样例同步更新。

## WebSocket稳定性改进详细说明

### 问题分析
1. **硬超时问题**：原有30秒硬超时机制导致无消息时强制断开
2. **心跳机制不匹配**：客户端主动ping机制与服务器不兼容
3. **重连逻辑过于激进**：频繁的token重置和短间隔重连
4. **连接管理复杂**：并发连接尝试导致"Connector is closed"错误

### 解决方案
1. **移除硬超时**：改为基于消息时间的被动检测（5分钟无消息超时）
2. **简化心跳**：仅响应服务器ping，不主动发送ping
3. **优化重连策略**：
   - 增加重连冷却时间（30秒）
   - 提高容错阈值（连续失败10次才重置token）
   - 基于连接时长判断是否重置token
4. **改进连接管理**：
   - 统一的安全关闭方法
   - 避免并发连接尝试
   - 增加连接超时设置

### 配置调整
- `max_consecutive_failures`: 3 → 10（提高容错性）
- `no_message_timeout`: 120 → 300秒（增加超时时间）
- `base_delay`: 5 → 10秒（减少重连频率）
- `max_delay`: 120 → 300秒（增加最大延迟）
- 新增 `reconnect_cooldown`: 30秒（重连冷却时间）

### 测试验证
- 创建了稳定性测试脚本 `websocket_stability_test.py`
- 更新了单元测试 `tests/test_websocket_heartbeat.py`

## 视频流稳定性改进详细说明

### 问题分析
1. **空帧被误判为失败**：所有空帧都计入连续失败，导致流过早停止
2. **容错性不足**：30次失败阈值对于网络视频流来说过于严格
3. **缺乏重试机制**：单次读取失败就计为失败，没有重试
4. **统计信息不完整**：无法区分真正的错误和正常的空帧

### 解决方案
1. **区分空帧和失败**：
   - 空帧（ret=True但frame为空）不计入失败
   - 真正的读取失败（ret=False）才计入失败
   - 分别设置空帧和失败的阈值

2. **增加重试机制**：
   - 帧读取失败时自动重试3次
   - 重试间隔0.1秒，避免过度占用资源

3. **提高容错性**：
   - 失败阈值：30 → 100次
   - 空帧阈值：200次（单独计算）
   - 生产者连续失败：8 → 20次
   - 生产者连续空帧：50次

4. **优化性能**：
   - 空帧后休眠时间：0.05秒（减少延迟）
   - 失败后休眠时间：2.0 → 1.0秒
   - 减少不必要的日志输出

### 配置调整
- `read_fail_threshold`: 30 → 100
- `empty_frame_threshold`: 200（新增）
- `max_consecutive_failures`: 8 → 20
- `max_consecutive_empty_frames`: 50（新增）
- `frame_read_retry`: 3（新增）
- `empty_frame_sleep_time`: 0.05秒（新增）

### 测试验证
- 创建了视频流稳定性测试脚本 `video_stream_stability_test.py`
- 增强了统计信息，区分成功帧、空帧和失败

## 流检查逻辑优化详细说明

### 问题分析
1. **重启后立即失败**：新流处理器启动时，生产者可能还没完全初始化
2. **检查逻辑过严**：要求连接管理器和生产者都活跃，但停止时连接管理器被设为非活跃
3. **检查频率过高**：每50次空帧就检查，在网络不稳定时造成过度检查
4. **缺乏启动缓冲**：没有给新启动的流足够的初始化时间

### 解决方案
1. **增加启动缓冲期**：
   - 流启动后10秒内假设健康，不进行严格检查
   - 流处理器启动前等待3秒，让流稳定

2. **放宽健康检查条件**：
   - 主要检查生产者是否运行，而不是连接管理器状态
   - 连接管理器状态可能因临时网络问题变化

3. **优化检查频率**：
   - 流状态检查：每50次 → 每100次空帧
   - 检查超时：3秒 → 5秒
   - 增加超时异常处理

4. **进一步提高容错性**：
   - 空帧阈值：200 → 500次
   - 生产者连续失败：20 → 30次
   - 生产者连续空帧：50 → 100次

### 配置调整
- `empty_frame_threshold`: 200 → 500
- `max_consecutive_failures`: 20 → 30
- `max_consecutive_empty_frames`: 50 → 100
- 新增启动缓冲期：10秒
- 新增初始化等待：3秒

### 测试验证
- 创建了流稳定性改进测试脚本 `test_stream_stability_improvements.py`
- 模拟网络不稳定情况下的流处理
- 监控重启次数和恢复能力

（本文件为 v4，包含WebSocket、视频流稳定性和流检查逻辑优化）

