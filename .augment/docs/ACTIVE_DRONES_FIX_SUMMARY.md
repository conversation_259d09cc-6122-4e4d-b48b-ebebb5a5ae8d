# 活跃无人机获取逻辑简化重构总结

## 问题分析

原有的活跃无人机判断逻辑过于复杂，存在以下问题：

### 1. 逻辑过于复杂
- 多层缓存机制增加了复杂性
- 多个过滤参数导致调用混乱
- 配置过滤逻辑在多个地方重复实现

### 2. 无法正确识别活跃状态
- 测试发现无法正确识别无人机是否活跃
- 调用链路混乱，不同组件获取的结果不一致
- 缓存机制导致状态更新延迟

### 3. 调试困难
- 缺乏清晰的日志输出
- 复杂的逻辑难以排查问题

## 简化重构方案

### 核心设计原则

根据用户需求，活跃无人机判断条件简化为：
1. **只检查配置列表中的无人机** (`drone_list`)
2. **有 WebSocket 数据** (最近更新且模式代码活跃)
3. **有视频流** (后续扩展)

### 1. 简化 SharedStateManager 核心逻辑

**修改文件**: `app/utils/shared_state.py`

**主要改动**:
- 移除复杂的缓存机制和多参数设计
- 简化为单一的 `get_active_drones()` 方法
- 只检查配置列表中的无人机
- 增加详细的调试日志

**核心逻辑**:
```python
def get_active_drones(self) -> Dict[str, Dict[str, Any]]:
    """只检查配置列表中的无人机，且满足活跃条件"""
    config_drone_codes = config.get_drone_codes()
    active_drones_result = {}

    for drone_code in config_drone_codes:
        if self._is_drone_active_simple(drone_code, current_time):
            status = active_drones.get(drone_code, {})
            active_drones_result[drone_code] = status

    return active_drones_result

def _is_drone_active_simple(self, drone_code: str, current_time: float) -> bool:
    """简化的活跃检查：WebSocket数据 + 视频流(可扩展)"""
    return self._has_websocket_data(drone_code, current_time)
```

### 2. 移除重复的配置过滤逻辑

**修改文件**:
- `app/database/drone_info.py`
- `app/database/websocket_client.py`
- `app/processor/stream_processor.py`
- `app/services/monitor_service.py`

**主要改动**:
- 移除所有组件中的重复配置过滤逻辑
- 统一使用 `shared_state_manager.get_active_drone_ids()` 获取活跃无人机
- 简化方法签名，移除不必要的参数

### 3. 增强调试能力

**主要改进**:
- 为每个检查步骤添加详细的 DEBUG 日志
- 使用 INFO 级别日志记录关键状态变化
- 清晰的成功/失败标识 (✅/❌)
- 显示具体的检查条件和结果

## 测试验证

创建了测试脚本 `tests/test_shared_state_only.py` 来验证简化后的效果：

### 测试场景
1. **配置列表检查**: 只检查配置文件中指定的无人机
2. **WebSocket 数据验证**: 检查无人机是否有有效的 WebSocket 数据
3. **活跃状态判断**: 验证综合的活跃状态判断逻辑
4. **单个无人机检查**: 验证 `is_drone_active()` 方法正常工作

### 测试结果
✅ 测试通过，简化后的逻辑工作正常

**关键测试输出**:
```
开始检查配置列表中的无人机: ['1581F6Q8D24AH00G736E', ...]
✅ 无人机 1581F6Q8D24AH00G736E 活跃
❌ 无人机 1581F6Q8D24AB00GJ00Q 不活跃 (不在 active_drones 中)
活跃无人机检查完成，共 1 个活跃: ['1581F6Q8D24AH00G736E']
```

## 简化重构效果

### 重构前的问题
- 逻辑过于复杂，难以理解和维护
- 无法正确识别无人机活跃状态
- 缓存机制增加了不必要的复杂性
- 调试困难，缺乏清晰的日志

### 重构后的效果
- ✅ **逻辑简化**: 移除复杂的缓存和多参数设计
- ✅ **功能正确**: 能够正确识别活跃无人机状态
- ✅ **易于调试**: 详细的日志输出，清晰的成功/失败标识
- ✅ **性能优化**: 直接检查，避免缓存开销
- ✅ **易于扩展**: 预留视频流检查接口
- ✅ **统一标准**: 所有组件使用相同的活跃判断逻辑

## 使用说明

### 获取活跃无人机列表（简化版本）
```python
from app.utils.shared_state import shared_state_manager

# 获取活跃无人机字典（包含详细状态信息）
active_drones = shared_state_manager.get_active_drones()

# 获取活跃无人机ID列表
active_drone_ids = shared_state_manager.get_active_drone_ids()
```

### 检查单个无人机是否活跃
```python
from app.utils.shared_state import shared_state_manager
is_active = shared_state_manager.is_drone_active('drone_code')
```

### 活跃判断条件
1. **配置检查**: 无人机必须在 `config.yaml` 的 `drone_list` 中
2. **WebSocket 数据**: 必须有最近的 WebSocket 数据（5分钟内）
3. **模式代码**: 模式代码必须在活跃列表中
4. **活跃标记**: `is_active` 字段必须为 `True`

## 注意事项

1. **简化设计**: 移除了复杂的参数和缓存，现有代码需要更新调用方式
2. **配置依赖**: 确保 `config.yaml` 中的 `drone_list` 配置正确
3. **性能优化**: 直接检查避免缓存开销，适合实时性要求高的场景
4. **调试支持**: 详细的调试日志，设置日志级别为 DEBUG 可查看完整检查过程
5. **扩展性**: 预留了视频流检查接口，可根据需要扩展

## 后续扩展

### 视频流检查
可以在 `_is_drone_active_simple()` 方法中添加视频流检查：
```python
def _is_drone_active_simple(self, drone_code: str, current_time: float) -> bool:
    # 1. 检查 WebSocket 数据
    if not self._has_websocket_data(drone_code, current_time):
        return False

    # 2. 检查视频流（扩展功能）
    if not self._has_video_stream(drone_code):
        logger.debug(f"无人机 {drone_code} 没有视频流")
        return False

    return True
```

## 相关文件

- `app/utils/shared_state.py` - 核心状态管理（重构）
- `app/database/drone_info.py` - 无人机信息管理（简化）
- `app/database/websocket_client.py` - WebSocket 客户端（简化）
- `app/processor/stream_processor.py` - 流处理器（简化）
- `app/services/monitor_service.py` - 监控服务（简化）
- `tests/test_shared_state_only.py` - 测试脚本（更新）
