# 8月23日程序中断问题排查方案

## 问题背景

程序在2025年8月23日 09:58:30中断，中断前最后日志显示：
- 活跃无人机检查完成: 0/17 个活跃
- 流监控器获取到活跃无人机列表: []
- 随后出现YOLO/Ultralytics相关初始化信息
- 程序中断，无明显异常日志

## 1. 问题分析与排查思路

### 1.1 问题分类

基于代码审查，将潜在问题分为三类：

#### 🔴 高优先级：内存泄漏问题
1. **YOLO模型重复实例化**
2. **FrameBuffer内存累积**
3. **数据库连接泄漏**

#### 🟡 中优先级：事件循环阻塞问题
1. **数据库同步操作阻塞**
2. **NFS操作阻塞**
3. **YOLO模型加载阻塞**

#### 🟢 低优先级：资源管理问题
1. **WebSocket消息处理累积**
2. **定时任务内存累积**

### 1.2 问题严重程度排序

**最可能导致中断的问题（按优先级）：**

1. **YOLO模型重复实例化内存泄漏** - 直接对应日志中的YOLO初始化信息
2. **FrameBuffer内存累积** - 90秒缓存 × 30fps = 2700帧，可能数GB内存
3. **数据库同步操作阻塞事件循环** - 在WebSocket/定时任务中被调用
4. **NFS操作阻塞事件循环** - 定时任务中的subprocess.run()调用

### 1.3 触发条件分析

即使"活跃无人机列表为空"，以下代码路径仍会运行：
- **WebSocket消息处理** - 持续接收消息并更新共享状态
- **APScheduler定时任务** - NFS检查(10s)、统计日志(30s)、健康检查(30s)
- **流监控器心跳** - 每3分钟运行一次
- **YOLO模型延迟初始化** - 可能在某些条件下触发

## 2. 调试日志添加策略

### 2.1 内存使用监控（高优先级）

#### 位置1：监控服务心跳增强
**文件：** `app/services/monitor_service.py`
**方法：** 心跳日志部分（约第339行）
**添加内容：**
```python
# 在现有心跳日志中增加内存监控
import psutil
import os
process = psutil.Process(os.getpid())
memory_mb = process.memory_info().rss / 1024 / 1024
cpu_percent = process.cpu_percent()

logger.info(f"监控服务心跳检查 - 运行状态: {self._running}, 任务数: {len(self._tasks)}, "
           f"内存使用: {memory_mb:.1f}MB, CPU: {cpu_percent:.1f}%")
```
**预期作用：** 每2分钟记录一次内存使用，用于判断是否存在内存泄漏

#### 位置2：流监控器心跳增强
**文件：** `app/processor/stream_processor.py`
**方法：** `_monitor_loop`中的心跳部分（约第438行）
**添加内容：**
```python
# 在流监控器心跳中增加内存和处理器状态
import psutil
import os
process = psutil.Process(os.getpid())
memory_mb = process.memory_info().rss / 1024 / 1024

logger.info(f"流监控器心跳检查 - 运行状态: {self.is_running}, 处理器数: {len(self.processors)}, "
           f"内存使用: {memory_mb:.1f}MB")
```
**预期作用：** 每3分钟记录一次内存使用和处理器数量

### 2.2 YOLO模型实例化监控（高优先级）

#### 位置3：模型创建监控
**文件：** `app/processor/image_processor.py`
**方法：** `_initialize_model`（第184行）
**添加内容：**
```python
def _initialize_model(self):
    if self.model_type == 'cv':
        import psutil
        import os
        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss / 1024 / 1024
        
        from app.models.yolo_model import YOLOModel
        model_path = config.YOLO_MODEL_PATH
        self.logger.info(f"[内存监控] 开始初始化YOLO模型，路径: {model_path}, 当前内存: {memory_before:.1f}MB")
        
        model = YOLOModel(model_path)
        
        memory_after = process.memory_info().rss / 1024 / 1024
        self.logger.info(f"[内存监控] YOLO模型初始化完成，内存变化: {memory_before:.1f}MB -> {memory_after:.1f}MB (+{memory_after-memory_before:.1f}MB)")
        
        return model
```
**预期作用：** 监控每次YOLO模型创建的内存影响

### 2.3 数据库操作耗时监控（中优先级）

#### 位置4：数据库连接耗时
**文件：** `app/database/drone_info.py`
**方法：** `_get_db_connection`（第310行）
**添加内容：**
```python
def _get_db_connection(self):
    import time
    t0 = time.monotonic()
    
    try:
        # 现有逻辑...
        if need_new:
            t_conn0 = time.monotonic()
            logger.info("[耗时监控] 开始建立数据库连接")
            self._db_connection = mysql.connector.connect(...)
            conn_duration = (time.monotonic() - t_conn0) * 1000
            if conn_duration > 1000:  # 超过1秒记录警告
                logger.warning(f"[耗时监控] 数据库连接建立耗时过长: {conn_duration:.1f}ms")
            else:
                logger.debug(f"[耗时监控] 数据库连接建立耗时: {conn_duration:.1f}ms")
        
        return self._db_connection
    finally:
        total_duration = (time.monotonic() - t0) * 1000
        if total_duration > 2000:  # 超过2秒记录警告
            logger.warning(f"[耗时监控] _get_db_connection 总耗时过长: {total_duration:.1f}ms")
```
**预期作用：** 监控数据库连接操作是否阻塞事件循环

### 2.4 FrameBuffer内存监控（中优先级）

#### 位置5：帧缓存状态监控
**文件：** `app/utils/frame_buffer.py`
**方法：** `add_frame`（第28行）
**添加内容：**
```python
def add_frame(self, frame: np.ndarray, timestamp: float) -> None:
    with self._lock:
        self.frame_count += 1
        frame_info = FrameInfo(...)
        self.frames.append(frame_info)
        self._clean_expired_frames(timestamp)
        
        # 每100帧记录一次缓存状态
        if self.frame_count % 100 == 0:
            frame_size_mb = frame.nbytes / 1024 / 1024
            total_memory_mb = len(self.frames) * frame_size_mb
            logger.info(f"[内存监控] FrameBuffer状态 - 帧数: {len(self.frames)}, "
                       f"预估内存: {total_memory_mb:.1f}MB, 单帧大小: {frame_size_mb:.2f}MB")
```
**预期作用：** 监控帧缓存的内存使用情况

### 2.5 NFS操作耗时监控（中优先级）

#### 位置6：NFS检查任务耗时
**文件：** `app/services/monitor_service.py`
**方法：** `_check_nfs_and_remount`（需要查找具体位置）
**添加内容：**
```python
async def _check_nfs_and_remount(self):
    import time
    t0 = time.monotonic()
    
    try:
        logger.debug("[耗时监控] 开始执行NFS检查任务")
        # 现有逻辑...
        result = nfs_manager.check_and_remount()
        
        duration = (time.monotonic() - t0) * 1000
        if duration > 5000:  # 超过5秒记录警告
            logger.warning(f"[耗时监控] NFS检查任务耗时过长: {duration:.1f}ms")
        else:
            logger.debug(f"[耗时监控] NFS检查任务耗时: {duration:.1f}ms")
            
    except Exception as e:
        duration = (time.monotonic() - t0) * 1000
        logger.error(f"[耗时监控] NFS检查任务异常，耗时: {duration:.1f}ms, 错误: {e}")
```
**预期作用：** 监控NFS操作是否阻塞事件循环

## 3. 验证和监控方案

### 3.1 问题判断标准

**内存泄漏判断：**
- 内存使用持续增长，每小时增长超过100MB
- YOLO模型创建次数异常增多
- FrameBuffer内存使用超过预期（>500MB）

**事件循环阻塞判断：**
- 数据库操作耗时超过2秒
- NFS操作耗时超过5秒
- 心跳日志间隔异常（超过预期时间）

### 3.2 监控指标和阈值

| 指标 | 正常范围 | 警告阈值 | 危险阈值 |
|------|----------|----------|----------|
| 进程内存使用 | <500MB | >1GB | >2GB |
| YOLO模型实例数 | 1-3个 | >5个 | >10个 |
| 数据库连接耗时 | <100ms | >1s | >2s |
| NFS操作耗时 | <1s | >5s | >10s |
| FrameBuffer内存 | <200MB | >500MB | >1GB |

### 3.3 问题复现后的分析方法

1. **查看内存趋势** - 分析心跳日志中的内存使用变化
2. **统计模型创建次数** - 搜索"开始初始化YOLO模型"日志
3. **检查阻塞操作** - 查找耗时超过阈值的操作
4. **分析时间线** - 对比各组件的最后活动时间

## 4. 注意事项

- **性能影响最小化** - 仅在关键且非高频位置添加日志
- **日志级别控制** - 正常情况使用DEBUG，异常情况使用WARNING/ERROR
- **避免日志洪水** - 使用计数器控制日志频率（如每100次记录一次）
- **生产环境适用** - 所有日志都考虑了生产环境的性能要求
