[说明] 本文档用于记录与“事件上报（EventReporter）”相关的线上告警排查思路、现象分析、定位过程、代码改动与验证步骤，帮助后续快速复盘与扩展。

## 一、问题描述与典型日志
- 线上频繁出现 Connection closed / Session is closed，导致：
  - getDroneEventData（事件上报）多次重试后仍失败；
  - receiveVideo（视频上报）也可能跟随失败；
  - monitor_service 定时输出事件循环漂移（drift > 2s）告警。
- 本地/单元测试通过，但线上仍失败，提示问题更偏运行时时序/内容格式/会话管理。

### 最新问题现象（2025-08-19）
```
2025-08-19 16:43:43,508 - app.utils.event_reporter - INFO - 正在调用事件上报接口: http://192.168.100.191:8082/webDroneClient/getDroneEventData
2025-08-19 16:43:43,514 - app.utils.event_reporter - DEBUG - [HTTP] 触发会话重置: Connection closed
2025-08-19 16:43:44,127 - app.utils.event_reporter - WARNING - [HTTP] 请求重试耗尽: ClientConnectionError: Connection closed
2025-08-19 16:43:44,127 - app.utils.event_reporter - WARNING - 事件上报重试后仍失败: 1581F6Q8X251H00G04Z4, 目标类型: 未知
```

**关键观察**：
- 所有重试（3次）都立即失败，提示连接被关闭
- 事件上报和视频上报都失败
- 单元测试通过，说明代码逻辑正确，问题在于网络层面

### 问题根因与修复（2025-08-20）

**根本原因**：`_post_cm_or_await`方法的实现有严重缺陷
- 原方法在`async with obj as resp:`中返回`resp`对象
- 当退出`async with`块后，响应连接被自动关闭
- 调用方再次访问响应时触发"Connection closed"错误

**修复方案**：重写`_post_cm_or_await`方法
- 在上下文管理器内完成响应内容读取
- 返回包含状态码和内容的简单响应对象
- 避免返回已关闭的连接对象

**修复验证**：
- ✅ 所有单元测试通过（5/5）
- ✅ 单个API调用成功
- ✅ 完整事件上报流程成功
- ✅ 连续多次调用成功
- ✅ 连接错误重试机制正常

示例（节选）：
- Connection closed 发生在进入 async with 或读取响应时；
- 事件循环延迟异常: drift≈3s；
- 之前日志对两个 API 的区分不足，影响快速定位。

## 二、根因假设（头脑风暴）
1) 事件循环被阻塞（重IO/CPU的同步操作在async线程中执行），导致网络请求握手/读写延迟，服务端主动断开。
2) aiohttp ClientSession 被跨事件循环/线程复用，出现已关闭/半关闭的连接继续被使用。
3) 同一时段请求过于密集（事件/视频先后上报 + 多无人机并发），叠加 1) 更易触发对端关闭。
4) 请求数据编码格式不符（服务器要求 multipart/form-data），对端立即关闭连接。

## 三、接口契约（来自实际联调与说明）
- getDroneEventData（form-data）：字段（共10）
  - imageinit, imageres, timestamp, err, camera, snNum, business_type, identifyType, longitude, latitude
- receiveVideo（form-data）：字段（共4）
  - video, snNum, timestamp, identifyType
- 注意：仅传“文件名”，不上传实际文件内容。图片/视频的真实保存不影响接口调用（可在调用前或调用后异步保存）。
  
## 四、已实施的代码改动（最小侵入但高效）
以下改动均已合入，以增强稳定性与可观测性：

1) 会话管理：按事件循环（loop）维度复用 ClientSession
- 位置：app/utils/event_reporter.py
- 变更：
  - self._sessions: dict[loop_id, ClientSession]；
  - self._session_locks: dict[loop_id, Lock]；
  - _get_session() 每个 loop 独立锁与独立会话；
  - _reset_session() 仅清除当前 loop 的会话引用；
  - 重试闭包（_post_once）每次重试重新获取会话，确保 _reset_session 生效。
- 目的：杜绝跨 loop 使用同一个 ClientSession 导致的“Session is closed/Connection closed”。

2) 请求体编码：明确改为 multipart/form-data
- 位置：app/utils/event_reporter.py
- 变更：新增 _build_form_data(payload) -> aiohttp.FormData，并在两个 API 的 POST 调用中使用；
- 目的：与服务端契约一致（与 Postman form-data 行为一致），避免对端立即关闭连接。

3) 连接策略：由强制关闭改为默认 keep-alive
- 位置：app/utils/event_reporter.py
- 变更：ClientSession 创建时不再强制 TCPConnector(force_close=True)，改用默认 keep-alive（并携带全局 timeout）。
- 目的：减少频繁握手对失败率的放大效应。

4) 轻量限流：默认最小请求间隔 0.1s（可配置）
- 位置：app/utils/event_reporter.py
- 变更：HTTP_MIN_INTERVAL（默认0.1s），按 loop 维度记录最近一次请求时间戳，避免瞬时拥挤。
- 目的：降低高峰期重试/并发抖动对远端的压力。

5) 去阻塞：把重 IO/CPU 操作移到线程池
- 位置：
  - app/processor/image_processor.py：保存图片（cv2.imwrite）改为 run_in_executor；
  - app/utils/file_manager.py：视频写入（cv2.VideoWriter）改为 run_in_executor；
- 目的：降低事件循环漂移（drift），避免网络请求在关键时段被阻塞。

6) 错误与重试增强
- 位置：app/utils/event_reporter.py
- 变更：
  - _with_retry 捕获 RuntimeError/ClientError/Timeout/ServerDisconnectedError；
  - 检测 "Connection closed"/"Session is closed" 时先 _reset_session 再退避重试；
  - 重试日志包含 attempt 计数与最终异常类型，便于快速归因。

7) 业务容错：无人机信息缺失不再短路
- 位置：app/utils/event_reporter.py
- 变更：未取到 drone_info 时仅记录错误，仍继续走事件/视频上报；snNum 使用 drone_code 回退。
- 目的：避免因信息缺失导致视频上报被跳过，降低误报警告。

8) 移除 headers={"Connection": "close"}
- 位置：app/utils/event_reporter.py
- 目的：避免与连接器策略叠加产生不必要的握手异常。

## 五、验证维度与步骤
1) 单元测试（安全、快速）
- 已通过：tests/event_reporter/test_event_reporter_session_closed.py
  - 覆盖点：Session is closed/Connection closed 重试；事件失败但视频成功整体 True；
- 运行命令：pytest -q tests/event_reporter/test_event_reporter_session_closed.py -q

2) 线上运行日志检查
- 关注点：
  - 是否仍出现“事件循环延迟异常 drift>2s”；
  - 若“请求重试耗尽”，查看打印的异常类型（例如 ClientConnectionError/Timeout 等）；
  - 是否能明确区分是事件 API 失败还是视频 API 失败（从调用位置与上下文可区分）。

3) 契约符合性
- 抓包或从服务端日志确认 Content-Type 为 multipart/form-data；
- 字段名与数量与上述契约一致；
- 值均为字符串（form-data 的文本字段）。

4) 行为正确性
- 无人机信息缺失时不应短路；
- 事件上报失败但视频成功时整体返回 True；
- 重 IO/CPU 操作不阻塞事件循环（drift 告警显著减少）。

## 六、可能的后续优化（如仍有异常）
1) 调整重试与退避策略（改为配置）
- base_delay、retries 暂时在代码默认值，可提升为配置项（例如 base_delay=0.5、retries=3）。

2) 连接策略微调
- 如果 keep-alive 环境仍不稳定，可尝试：
  - 使用 TCPConnector(limit/limit_per_host 调优)；
  - 设置连接超时/读超时更贴近网络实际；
  - 在网络不稳定场合重新启用 force_close，但配合更温和退避与更长最小间隔。

3) 日志标签细化
- 若你需要，我可在不影响单测的前提下，为事件/视频两个 API 加上独立的日志前缀（EventAPI/VideoAPI），便于一眼识别来源。

4) 保存流程进一步解耦
- 目前图片/视频写入已在后台线程进行，若还需进一步降低上报延迟，可考虑：
  - 先上报（仅文件名），保存动作下发到后台任务队列；
  - 失败补偿与幂等由任务层面解决。

## 七、系统性排查方案（2025-08-19 更新）

### 7.1 分层诊断策略
基于最新的Connection closed问题，采用分层诊断方法：

**第一层：网络连通性诊断**
- [ ] 运行网络诊断工具：`python -m tests.network_diagnostics`
- [ ] 检查DNS解析：目标域名是否可解析
- [ ] 检查TCP连接：目标端口是否可达
- [ ] 检查网络延迟：RTT是否异常

**第二层：HTTP协议诊断**
- [ ] 启用详细HTTP日志：设置环境变量 `EVENT_REPORTER_DEBUG=1`
- [ ] 检查请求格式：确认multipart/form-data格式正确
- [ ] 检查请求时序：各阶段耗时是否正常
- [ ] 检查响应处理：是否在特定阶段失败

**第三层：应用层诊断**
- [ ] 检查事件循环状态：是否有drift告警
- [ ] 检查资源使用：CPU/内存/文件描述符
- [ ] 检查并发情况：同时请求数量
- [ ] 检查配置参数：超时、重试、间隔设置

### 7.2 快速排查清单（原有）
- [ ] 线上是否仍有 drift 告警？发生时间与上报时段是否重合？
- [ ] 抓包/服务端确认请求为 multipart/form-data，字段名/数量完全匹配？
- [ ] “请求重试耗尽”的异常类型是什么？（连接错误/超时/服务端断开）
- [ ] 同时段 CPU/Disk/NFS 是否飙升？
- [ ] HTTP_MIN_INTERVAL 取值是否过小（建议 0.1~0.2s 试探）？

### 7.3 诊断工具使用
```bash
# 运行网络连通性测试
python -m tests.network_diagnostics

# 启用详细HTTP诊断日志
export EVENT_REPORTER_DEBUG=1
python your_main_app.py

# 运行增强的单元测试
pytest tests/event_reporter/ -v -s
```

## 八、变更影响范围
- app/utils/event_reporter.py：会话管理、重试逻辑、请求体编码、限流与日志；
- app/processor/image_processor.py：保存图片改为线程池执行；
- app/utils/file_manager.py：保存视频改为线程池执行；
- 逻辑兼容：未改变对外接口与调用方式；未新增依赖。

## 九、回滚与风险
- 若需回滚，可逐项回退：
  - 先回退 keep-alive -> force_close；
  - 再回退 form-data -> 原 data（不建议，除非服务端确认为非 multipart）；
  - 警惕回滚“线程池写入”会重新引入 drift 风险。

## 十、新增诊断功能说明（2025-08-19）

### 10.1 增强的日志诊断
- **详细HTTP请求日志**：记录请求阶段耗时、会话ID、状态码等
- **分层错误信息**：区分网络层、HTTP层、应用层错误
- **调试模式开关**：通过 `EVENT_REPORTER_DEBUG=1` 启用详细诊断

### 10.2 网络连通性诊断
- **启动时自动测试**：调试模式下自动测试目标接口连通性
- **分层网络测试**：DNS解析 → TCP连接 → HTTP连接
- **性能指标收集**：记录各阶段耗时，便于性能分析

### 10.3 独立诊断工具
- **位置**：`tests/network_diagnostics.py`
- **功能**：独立的网络连通性测试，不依赖主程序
- **使用**：`python -m tests.network_diagnostics`

### 10.4 增强的单元测试
- **位置**：`tests/event_reporter/test_event_reporter_diagnostics.py`
- **覆盖**：诊断功能、错误处理、调试模式等
- **运行**：`pytest tests/event_reporter/test_event_reporter_diagnostics.py -v`

## 十一、附录：常见现象与对策对照

### 11.1 Connection closed 问题分析
- **立即发生在请求初期**：优先检查请求体编码与会话复用
- **重试都失败但无 drift**：优先检查服务器端连接策略/限流/负载均衡
- **有明显 drift 同期**：优先排查本地阻塞（磁盘写、CPU峰值、NFS抖动）

### 11.2 基于诊断结果的排查策略
- **DNS解析失败**：检查网络配置、DNS服务器设置
- **TCP连接失败**：检查防火墙、目标服务状态、网络路由
- **HTTP连接成功但请求失败**：检查请求格式、认证、服务端限流
- **所有层面都正常但仍失败**：检查请求数据内容、服务端业务逻辑

### 11.3 性能优化建议
- **高延迟场景**：调整超时配置，增加重试间隔
- **高并发场景**：调整最小请求间隔，优化会话复用策略
- **不稳定网络**：启用更详细的诊断日志，收集更多上下文信息

