# 无人机视频流实时目标检测系统

基于FastAPI的智能无人机视频监控与AI目标检测系统，专为水域环境监测、安全巡检等应用场景设计。

## 🚀 项目概述

本系统是一个专业的无人机视频流实时处理和AI目标检测平台，集成了计算机视觉、多模态AI模型、WebSocket实时通信等先进技术，能够对20+架无人机进行并发监控，实现水域安全、人员安全、环境安全等多场景的智能检测。

### 核心优势

- **多输入源支持**：图像、视频文件、RTMP/WebRTC实时视频流
- **双AI模型架构**：YOLO计算机视觉 + Ollama多模态模型
- **实时流处理**：专门优化的无人机视频流处理管道
- **多无人机管理**：支持20+架无人机的并发监控
- **专业目标检测**：针对9类专业目标的精准识别
- **分布式文件系统**：NFS网络文件系统集成

## ✨ 功能特性

### 🎯 目标检测能力
- **水域安全**：船只、疑似异常藻类、曝气设备
- **人员安全**：人员识别和定位
- **设施安全**：管路异常检测
- **环境安全**：施工、车辆、卡车、火车识别
- **距离计算**：基于GPS坐标的目标距离判断

### 🔄 实时处理
- **视频流监控**：支持RTMP和WebRTC协议
- **帧缓存机制**：智能帧缓存管理，优化内存使用
- **自动重连**：流断连自动检测与重连
- **并发处理**：多线程并发处理多路视频流

### 🌐 通信与集成
- **WebSocket实时通信**：双向数据传输
- **REST API**：完整的HTTP API接口
- **事件上报**：检测结果自动上报外部系统
- **数据库集成**：MySQL数据持久化

## 🛠 技术栈

### 后端框架
- **FastAPI** - 现代高性能Web框架
- **Uvicorn** - ASGI服务器
- **APScheduler** - 定时任务调度

### AI与计算机视觉
- **Ultralytics YOLO11** - 目标检测主模型
- **Ollama** - 多模态AI模型服务
- **OpenCV** - 图像处理库
- **PIL** - 图像操作

### 实时通信
- **WebSocket** - 实时双向通信
- **aiortc** - WebRTC Python实现
- **aiohttp** - 异步HTTP客户端

### 数据存储
- **MySQL** - 关系型数据库
- **NFS** - 网络文件系统

### 其他工具
- **PyYAML** - 配置文件解析
- **asyncio** - 异步编程支持

## 📁 项目结构

```
CV/
├── main.py                     # 🚪 主程序入口
├── config_fake.yaml           # ⚙️ 系统配置文件
├── requirements.txt            # 📦 Python依赖包
├── websocket_test.py           # 🧪 WebSocket测试工具
├── app/                        # 📱 主应用目录
│   ├── api/                    # 🌐 API路由层
│   │   ├── detect.py           #   检测API (图像/视频/流)
│   │   └── monitor.py          #   监控API
│   ├── services/               # 🔧 业务服务层
│   │   └── monitor_service.py  #   监控服务核心
│   ├── processor/              # ⚡ 数据处理层
│   │   ├── image_processor.py  #   图像处理器
│   │   ├── stream_processor.py #   视频流处理器
│   │   └── video_processor.py  #   视频文件处理器
│   ├── models/                 # 🤖 AI模型封装
│   │   ├── yolo_model.py       #   YOLO模型接口
│   │   └── multimodal_model.py #   多模态模型接口
│   ├── database/               # 🗄️ 数据访问层
│   │   ├── drone_info.py       #   无人机信息管理
│   │   └── websocket_client.py #   WebSocket客户端
│   ├── utils/                  # 🛠️ 工具类库
│   │   ├── config.py           #   配置管理
│   │   ├── nfs_manager.py      #   NFS文件系统管理
│   │   ├── event_reporter.py   #   事件上报
│   │   └── ...                 #   其他工具类
│   ├── video/                  # 📹 视频流管理
│   └── templates/              # 🎨 前端模板
├── models/                     # 📊 AI模型文件
│   ├── pond_aeration_yolo11n.pt  # 专用YOLO模型
│   └── weights/                #   模型权重目录
├── tools/                      # 🔨 工具脚本
│   ├── llm_api.py             #   LLM API工具
│   └── web_scraper.py         #   网页爬虫工具
└── lib/                       # 📚 第三方库
    └── Real-ESRGAN/           #   超分辨率处理库
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- CUDA支持的GPU (推荐)
- MySQL 5.7+
- NFS服务器 (可选)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd CV
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置系统**
   ```bash
   # 复制配置模板
   cp config_fake.yaml config.yaml
   
   # 编辑配置文件
   vim config.yaml
   ```

4. **启动系统**
   ```bash
   # 开发模式
   python main.py
   
   # 或使用uvicorn直接启动
   uvicorn main:app --host 0.0.0.0 --port 8000
   ```

### 基础配置

#### 1. 运行模式配置
```yaml
mode:
  run_mode: "dev"  # dev/prod
  dev:
    enable_api: true
    enable_web: true
    port: 8000
  prod:
    enable_api: true
    enable_web: false
    port: 8001
```

#### 2. AI模型配置
```yaml
models:
  yolo:
    model_path: ./models/pond_aeration_yolo11n.pt
  multimodal:
    ollama_url: http://localhost:11434
    local_model: minicpm-v:8b
```

#### 3. 数据库配置
```yaml
database:
  host: localhost
  port: 3306
  user: your_username
  password: your_password
  database: your_database
```

## 📖 使用方法

### 启动方式

```bash
# 标准启动
python main.py

# 指定配置文件
python main.py --config config.yaml

# 调试模式
python main.py --debug
```

### Web界面

系统提供直观的Web管理界面：

- **主页**: `http://localhost:8000/`
- **图像检测**: `http://localhost:8000/api/`
- **视频检测**: `http://localhost:8000/api/video`
- **流检测**: `http://localhost:8000/api/stream`
- **WebRTC测试**: `http://localhost:8000/api/webrtc`

### API使用示例

#### 图像检测
```bash
curl -X POST "http://localhost:8000/api/image" \
  -F "file=@test_image.jpg" \
  -F "prompt=boat" \
  -F "model_type=cv"
```

#### 视频检测
```bash
curl -X POST "http://localhost:8000/api/video" \
  -F "file=@test_video.mp4" \
  -F "prompt=person" \
  -F "model_type=cv" \
  -F "max_duration=30"
```

#### 视频流检测
```bash
curl -X POST "http://localhost:8000/api/stream" \
  -F "stream_url=rtmp://example.com/live/stream" \
  -F "prompt=boat" \
  -F "model_type=cv"
```

## 🔌 API文档

### 检测接口

| 端点 | 方法 | 描述 | 参数 |
|------|------|------|------|
| `/api/image` | POST | 图像目标检测 | file, prompt, model_type |
| `/api/video` | POST | 视频文件检测 | file, prompt, model_type, max_duration |
| `/api/stream` | POST | 视频流检测 | stream_url, prompt, model_type |
| `/api/stream/stop` | POST | 停止流检测 | client_id |

### WebSocket接口

| 端点 | 协议 | 描述 |
|------|------|------|
| `/api/ws/stream/{client_id}` | WebSocket | 实时流检测数据推送 |

### 管理接口

| 端点 | 方法 | 描述 | 权限 |
|------|------|------|------|
| `/api/reload_model` | POST | 热更新AI模型 | API Key |

### 响应格式

#### 检测成功响应
```json
{
  "detections": [
    {
      "target_id": "1",
      "target_name": "船只",
      "box": [x1, y1, x2, y2],
      "confidence": 0.95,
      "label": "boat"
    }
  ],
  "count": 1,
  "annotated_image": "base64_encoded_image"
}
```

#### 错误响应
```json
{
  "error": "错误描述",
  "details": "详细错误信息"
}
```

## ⚙️ 高级配置

### 目标检测配置

系统支持9类专业目标检测：

```yaml
detection:
  check_distance: true
  target_ids: ["1", "2", "3", "4", "5", "6", "7", "8", "9"]
  targets:
    - id: 1
      name: "船只"
      category: "水域安全"
      keyword: "boat"
      distance: 50
      type: "drone_location"
    - id: 2
      name: "人员"
      category: "人员安全"
      keyword: "person"
      distance: 50
      type: "drone_location"
    # ... 更多目标配置
```

### 视频流配置

```yaml
video_stream:
  stream_type: "rtmp"  # rtmp/webrtc
  rtmp:
    base_url: "rtmp://server:1935/live/"
  webrtc:
    base_url: "webrtc://server:1935/live/"
    ice_servers:
      - urls: ["stun:stun.l.google.com:19302"]
  
  # 性能参数
  read_fail_threshold: 30
  buffer_duration: 30
  detection_interval: 0.5
  frame_rate: 30
  reconnect_interval: 5
```

### NFS文件系统配置

```yaml
storage:
  nfs:
    server: "*************"
    remote_paths:
      base: "/LgomDroneData"
      image: "/LgomDroneData/imageFile"
      video: "/LgomDroneData/videoFile"
  
  mount_points:
    dev:
      base: ./app_data/LgomDroneData
      image: ./app_data/LgomDroneData/imageFile
      video: ./app_data/LgomDroneData/videoFile
    prod:
      base: /mnt/LgomDroneData
      image: /mnt/LgomDroneData/imageFile
      video: /mnt/LgomDroneData/videoFile
```

### 安全配置

```yaml
api:
  security:
    api_key: "your-secure-api-key-here"
    allowed_ips:
      - "127.0.0.1"
      - "*************"
    rate_limit:
      max_requests: 5
      time_window: 60
```

## 🚀 部署指南

### Docker部署

```dockerfile
# Dockerfile示例
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "main.py"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  cv-system:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./config.yaml:/app/config.yaml
      - ./models:/app/models
      - ./app_data:/app/app_data
    environment:
      - PYTHONPATH=/app
    depends_on:
      - mysql
      - ollama

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: drone_detection
    ports:
      - "3306:3306"

  ollama:
    image: ollama/ollama
    ports:
      - "11434:11434"
```

### 生产环境部署

1. **系统服务化**
   ```bash
   # 创建systemd服务文件
   sudo nano /etc/systemd/system/cv-system.service
   ```

   ```ini
   [Unit]
   Description=CV Detection System
   After=network.target

   [Service]
   Type=simple
   User=www-data
   WorkingDirectory=/opt/cv-system
   ExecStart=/opt/cv-system/venv/bin/python main.py
   Restart=always

   [Install]
   WantedBy=multi-user.target
   ```

2. **Nginx反向代理**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }

       location /api/ws/ {
           proxy_pass http://127.0.0.1:8000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
       }
   }
   ```

## 🔧 开发与测试

### 测试工具

系统提供了完整的测试工具：

1. **WebSocket测试**
   ```bash
   python websocket_test.py
   ```

2. **API测试**
   ```bash
   pytest tests/
   ```

### 开发模式

```bash
# 启用开发模式
export RUN_MODE=dev
python main.py

# 或修改配置文件
mode:
  run_mode: "dev"
```

### 调试配置

```yaml
logging:
  level: DEBUG
  file:
    level: DEBUG
    path: ./app_data/logs/
```

## 🔍 监控与日志

### 日志系统

- **控制台日志**：实时查看系统运行状态
- **文件日志**：持久化日志存储
- **结构化日志**：JSON格式，便于分析

### 性能监控

- **流处理性能**：帧率、延迟统计
- **检测性能**：推理时间、精度指标
- **系统资源**：CPU、内存、GPU使用率

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📝 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 支持与帮助

- **问题反馈**：[GitHub Issues](https://github.com/your-repo/issues)
- **技术文档**：查看 `docs/` 目录
- **API文档**：启动服务后访问 `/docs`

## 🏆 致谢

- [FastAPI](https://fastapi.tiangolo.com/) - 现代Web框架
- [Ultralytics](https://ultralytics.com/) - YOLO模型
- [OpenCV](https://opencv.org/) - 计算机视觉库
- [Ollama](https://ollama.ai/) - 本地AI模型服务

---

**🌟 如果这个项目对您有帮助，请给我们一个Star！** 