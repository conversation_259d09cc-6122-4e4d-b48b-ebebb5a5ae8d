# 工具目录

本目录包含项目的各种实用工具。

## 可用工具

### 性能基准测试工具 (`performance_benchmark.py`)

专业的YOLO模型性能评估工具，用于测试模型在CPU和GPU模式下的推理性能。

**主要功能**：
- CPU和GPU模式性能对比测试
- 详细的资源使用监控和分析
- 完整的性能报告生成
- 基线和增量资源分析

**快速使用**：
```bash
# 基本测试
python tools/performance_benchmark.py

# 自定义参数
python tools/performance_benchmark.py --max-images 20 --verbose
```

**完整文档**：请参阅 `.augment/docs/performance_benchmark_complete_guide.md`

### 其他工具

- `llm_api.py` - LLM API接口工具
- `nfs_sync_simple.py` - NFS同步工具
- `search_engine.py` - 搜索引擎工具
- `web_scraper.py` - 网页抓取工具

## 添加新工具

在此目录中添加新工具时，请：

1. 使用描述性的文件名
2. 在文件开头添加详细的文档字符串
3. 包含命令行参数支持
4. 添加适当的错误处理
5. 在 `tests/unit/` 目录下创建对应的测试文件
6. 更新此README文件

## 测试

运行工具测试：
```bash
# 运行所有工具测试
python -m pytest tests/unit/ -v

# 运行特定工具测试
python -m pytest tests/unit/test_performance_benchmark.py -v
```

## 依赖管理

工具的依赖应该添加到项目根目录的 `requirements.txt` 文件中。
