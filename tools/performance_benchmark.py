#!/usr/bin/env python3
"""
性能基准测试工具

该工具用于测试模型在CPU和GPU模式下的性能表现，包括：
- 推理时间测量
- 资源使用监控（内存、显存、CPU/GPU使用率）
- 性能对比分析
- 详细报告生成

使用方法：
    python tools/performance_benchmark.py
"""

import os
import sys
import time
import json
import logging
import argparse
import threading
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import statistics

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import cv2
import torch
import numpy as np
import psutil
from ultralytics import YOLO

# 导入项目模块
from app.models.yolo_model import YOLOModel
from app.utils.config import config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ResourceMonitor:
    """资源监控器，用于监控CPU、内存、GPU使用情况，支持基线测量和增量分析"""

    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.cpu_usage = []
        self.memory_usage = []
        self.gpu_usage = []
        self.gpu_memory_usage = []

        # CPU增强监控：按核心采样、活跃核心数与有效核心数
        self.cpu_per_core_samples = []  # List[List[float]]
        self.active_cores = []          # List[int]
        self.effective_cores = []       # List[float]，等效核心数=∑(各核利用率%)/100
        self.cpu_active_threshold = 5.0 # 活跃核心阈值（%）

        # 基线和快照数据
        self.baseline_snapshot = None
        self.model_loaded_snapshot = None

        # 尝试导入pynvml以获得更准确的GPU监控
        self.pynvml_available = False
        try:
            import pynvml
            pynvml.nvmlInit()
            self.pynvml = pynvml
            self.gpu_handle = pynvml.nvmlDeviceGetHandleByIndex(0) if torch.cuda.is_available() else None
            self.pynvml_available = True
            logger.debug("PYNVML GPU监控已启用")
        except Exception as e:
            logger.debug(f"PYNVML不可用，使用PyTorch GPU监控: {e}")
            self.pynvml = None
            self.gpu_handle = None

    def _get_current_resource_snapshot(self, device_mode: str = 'auto') -> Dict[str, Any]:
        """
        获取当前资源使用快照

        Args:
            device_mode: 'cpu', 'cuda', 或 'auto' - 指定监控模式
        """
        # 多次采样CPU使用率以提高稳定性
        cpu_samples = []
        for _ in range(3):
            cpu_samples.append(psutil.cpu_percent(interval=0.1))

        snapshot = {
            'timestamp': time.time(),
            'cpu_percent': statistics.mean(cpu_samples),
            'memory_used_mb': psutil.virtual_memory().used / 1024 / 1024,
            'memory_total_mb': psutil.virtual_memory().total / 1024 / 1024,
            'memory_percent': psutil.virtual_memory().percent
        }

        # GPU监控 - 根据设备模式调整监控策略
        if torch.cuda.is_available():
            try:
                # 始终获取PyTorch GPU内存信息
                torch_allocated = torch.cuda.memory_allocated() / 1024 / 1024
                torch_reserved = torch.cuda.memory_reserved() / 1024 / 1024

                if device_mode == 'cpu':
                    # CPU模式：只监控PyTorch GPU内存，不监控系统级GPU使用
                    snapshot.update({
                        'gpu_utilization_percent': 0,  # CPU模式下不监控GPU使用率
                        'gpu_memory_utilization_percent': 0,
                        'gpu_memory_used_mb': torch_allocated,  # 使用PyTorch分配的内存
                        'gpu_memory_total_mb': 0,
                        'gpu_memory_free_mb': 0,
                        'torch_gpu_memory_allocated_mb': torch_allocated,
                        'torch_gpu_memory_reserved_mb': torch_reserved
                    })
                    logger.debug("CPU模式：仅监控PyTorch GPU内存")

                elif self.pynvml_available and self.gpu_handle:
                    # GPU模式：使用PYNVML获取完整的GPU信息
                    gpu_util = self.pynvml.nvmlDeviceGetUtilizationRates(self.gpu_handle)
                    memory_info = self.pynvml.nvmlDeviceGetMemoryInfo(self.gpu_handle)

                    snapshot.update({
                        'gpu_utilization_percent': gpu_util.gpu,
                        'gpu_memory_utilization_percent': gpu_util.memory,
                        'gpu_memory_used_mb': memory_info.used / 1024 / 1024,
                        'gpu_memory_total_mb': memory_info.total / 1024 / 1024,
                        'gpu_memory_free_mb': memory_info.free / 1024 / 1024,
                        'torch_gpu_memory_allocated_mb': torch_allocated,
                        'torch_gpu_memory_reserved_mb': torch_reserved
                    })
                    logger.debug("GPU模式：使用PYNVML完整监控")
                else:
                    # 回退到PyTorch监控
                    snapshot.update({
                        'gpu_utilization_percent': 0,  # PyTorch无法准确获取GPU使用率
                        'gpu_memory_utilization_percent': 0,
                        'gpu_memory_used_mb': torch_allocated,  # 使用PyTorch分配的内存
                        'gpu_memory_total_mb': 0,
                        'gpu_memory_free_mb': 0,
                        'torch_gpu_memory_allocated_mb': torch_allocated,
                        'torch_gpu_memory_reserved_mb': torch_reserved
                    })
                    logger.debug("使用PyTorch GPU监控（功能有限）")

            except Exception as e:
                logger.debug(f"GPU监控失败: {e}")
                snapshot.update({
                    'gpu_utilization_percent': 0,
                    'gpu_memory_utilization_percent': 0,
                    'gpu_memory_used_mb': 0,
                    'gpu_memory_total_mb': 0,
                    'gpu_memory_free_mb': 0,
                    'torch_gpu_memory_allocated_mb': 0,
                    'torch_gpu_memory_reserved_mb': 0
                })
        else:
            snapshot.update({
                'gpu_utilization_percent': 0,
                'gpu_memory_utilization_percent': 0,
                'gpu_memory_used_mb': 0,
                'gpu_memory_total_mb': 0,
                'gpu_memory_free_mb': 0,
                'torch_gpu_memory_allocated_mb': 0,
                'torch_gpu_memory_reserved_mb': 0
            })

        return snapshot

    def take_baseline_snapshot(self, device_mode: str = 'auto'):
        """记录系统基线资源使用情况"""
        logger.info("记录系统基线资源使用情况...")
        self.baseline_snapshot = self._get_current_resource_snapshot(device_mode)
        logger.info(f"基线快照: CPU={self.baseline_snapshot['cpu_percent']:.1f}%, "
                   f"内存={self.baseline_snapshot['memory_used_mb']:.1f}MB, "
                   f"GPU={self.baseline_snapshot['gpu_utilization_percent']:.1f}%, "
                   f"显存={self.baseline_snapshot['gpu_memory_used_mb']:.1f}MB")

    def take_model_loaded_snapshot(self, device_mode: str = 'auto'):
        """记录模型加载后的资源使用情况"""
        logger.info("记录模型加载后资源使用情况...")
        self.model_loaded_snapshot = self._get_current_resource_snapshot(device_mode)
        logger.info(f"模型加载后快照: CPU={self.model_loaded_snapshot['cpu_percent']:.1f}%, "
                   f"内存={self.model_loaded_snapshot['memory_used_mb']:.1f}MB, "
                   f"GPU={self.model_loaded_snapshot['gpu_utilization_percent']:.1f}%, "
                   f"显存={self.model_loaded_snapshot['gpu_memory_used_mb']:.1f}MB")

    def start_monitoring(self):
        """开始监控资源使用情况"""
        self.monitoring = True
        self.cpu_usage.clear()
        self.memory_usage.clear()
        self.gpu_usage.clear()
        self.gpu_memory_usage.clear()
        # 清理CPU增强监控数据
        self.cpu_per_core_samples.clear()
        self.active_cores.clear()
        self.effective_cores.clear()

        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info("开始资源监控")

    def stop_monitoring(self):
        """停止监控资源使用情况"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        logger.info("停止资源监控")

    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 监控CPU和内存（支持按核心采样）
                per_core = psutil.cpu_percent(interval=0.1, percpu=True)
                # 兼容某些mock/平台返回标量的情况
                if not isinstance(per_core, list):
                    try:
                        per_core = [float(per_core)]
                    except Exception:
                        per_core = [psutil.cpu_percent(interval=None)]
                overall_cpu = sum(per_core) / max(1, len(per_core))
                memory_info = psutil.virtual_memory()

                self.cpu_usage.append(overall_cpu)
                self.cpu_per_core_samples.append(per_core)
                # 统计活跃/有效核心数
                active = sum(1 for x in per_core if x >= self.cpu_active_threshold)
                effective = sum(per_core) / 100.0
                self.active_cores.append(active)
                self.effective_cores.append(effective)

                self.memory_usage.append(memory_info.used / 1024 / 1024)  # MB

                # 监控GPU（如果可用）
                if torch.cuda.is_available():
                    try:
                        if self.pynvml_available and self.gpu_handle:
                            # 使用PYNVML获取GPU使用率
                            gpu_util = self.pynvml.nvmlDeviceGetUtilizationRates(self.gpu_handle)
                            memory_info_gpu = self.pynvml.nvmlDeviceGetMemoryInfo(self.gpu_handle)

                            self.gpu_usage.append(gpu_util.gpu)
                            self.gpu_memory_usage.append(memory_info_gpu.used / 1024 / 1024)
                        else:
                            # 回退到PyTorch监控（注意：这里不使用torch.cuda.utilization()）
                            # 因为它可能不准确，我们只监控显存使用
                            gpu_memory = torch.cuda.memory_allocated() / 1024 / 1024  # MB
                            self.gpu_usage.append(0)  # 无法准确获取GPU使用率
                            self.gpu_memory_usage.append(gpu_memory)

                    except Exception as e:
                        logger.debug(f"GPU监控失败: {e}")

                time.sleep(0.1)  # 每100ms采样一次

            except Exception as e:
                logger.error(f"资源监控异常: {e}")
                break
                
    def get_stats(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        stats = {
            'cpu': {
                'avg': statistics.mean(self.cpu_usage) if self.cpu_usage else 0,
                'max': max(self.cpu_usage) if self.cpu_usage else 0,
                'min': min(self.cpu_usage) if self.cpu_usage else 0,
                'samples': len(self.cpu_usage)
            },
            'memory': {
                'avg': statistics.mean(self.memory_usage) if self.memory_usage else 0,
                'max': max(self.memory_usage) if self.memory_usage else 0,
                'min': min(self.memory_usage) if self.memory_usage else 0,
                'samples': len(self.memory_usage)
            }
        }

        # CPU增强统计：活跃/有效核心与每核心分布
        if self.cpu_per_core_samples:
            max_len = max(len(s) for s in self.cpu_per_core_samples)
            per_core_series = [
                [s[i] for s in self.cpu_per_core_samples if i < len(s)]
                for i in range(max_len)
            ]
            per_core_avg = [statistics.mean(series) if series else 0 for series in per_core_series]
            per_core_max = [max(series) if series else 0 for series in per_core_series]
            per_core_min = [min(series) if series else 0 for series in per_core_series]

            stats['cpu_per_core'] = {
                'avg': per_core_avg,
                'max': per_core_max,
                'min': per_core_min,
                'samples': len(self.cpu_per_core_samples)
            }
            if self.active_cores:
                stats['cpu_active_cores'] = {
                    'avg': statistics.mean(self.active_cores),
                    'max': max(self.active_cores),
                    'min': min(self.active_cores),
                    'samples': len(self.active_cores)
                }
            if self.effective_cores:
                stats['cpu_effective_cores'] = {
                    'avg': statistics.mean(self.effective_cores),
                    'max': max(self.effective_cores),
                    'min': min(self.effective_cores),
                    'samples': len(self.effective_cores)
                }

        if torch.cuda.is_available() and self.gpu_usage:
            stats['gpu'] = {
                'avg': statistics.mean(self.gpu_usage),
                'max': max(self.gpu_usage),
                'min': min(self.gpu_usage),
                'samples': len(self.gpu_usage)
            }
            stats['gpu_memory'] = {
                'avg': statistics.mean(self.gpu_memory_usage),
                'max': max(self.gpu_memory_usage),
                'min': min(self.gpu_memory_usage),
                'samples': len(self.gpu_memory_usage)
            }

        return stats

    def get_incremental_analysis(self) -> Dict[str, Any]:
        """获取增量分析，包括基线对比和各阶段资源增量"""
        analysis = {
            'baseline': self.baseline_snapshot,
            'model_loaded': self.model_loaded_snapshot,
            'inference_stats': self.get_stats()
        }

        # 计算模型加载导致的资源增量
        if self.baseline_snapshot and self.model_loaded_snapshot:
            # 使用更稳定的GPU内存指标（PyTorch分配的内存）
            baseline_gpu_mem = self.baseline_snapshot.get('torch_gpu_memory_allocated_mb', 0)
            loaded_gpu_mem = self.model_loaded_snapshot.get('torch_gpu_memory_allocated_mb', 0)

            analysis['model_loading_increment'] = {
                'cpu_percent_increase': self.model_loaded_snapshot['cpu_percent'] - self.baseline_snapshot['cpu_percent'],
                'memory_mb_increase': self.model_loaded_snapshot['memory_used_mb'] - self.baseline_snapshot['memory_used_mb'],
                'gpu_utilization_increase': self.model_loaded_snapshot['gpu_utilization_percent'] - self.baseline_snapshot['gpu_utilization_percent'],
                'gpu_memory_mb_increase': loaded_gpu_mem - baseline_gpu_mem,  # 使用PyTorch内存
                'torch_gpu_memory_mb_increase': loaded_gpu_mem - baseline_gpu_mem
            }

        # 计算推理过程中的资源增量（相对于模型加载后的状态）
        if self.model_loaded_snapshot and self.memory_usage:
            current_memory_avg = statistics.mean(self.memory_usage)
            # 使用PyTorch GPU内存而不是系统GPU内存
            current_gpu_memory_avg = statistics.mean(self.gpu_memory_usage) if self.gpu_memory_usage else 0

            # 获取模型加载后的GPU内存基准
            loaded_gpu_mem = self.model_loaded_snapshot.get('torch_gpu_memory_allocated_mb', 0)

            analysis['inference_increment'] = {
                'memory_mb_increase': current_memory_avg - self.model_loaded_snapshot['memory_used_mb'],
                'gpu_memory_mb_increase': max(0, current_gpu_memory_avg - loaded_gpu_mem)  # 确保非负
            }

        # 计算总增量（相对于基线）
        if self.baseline_snapshot and self.memory_usage:
            current_memory_avg = statistics.mean(self.memory_usage)
            current_gpu_memory_avg = statistics.mean(self.gpu_memory_usage) if self.gpu_memory_usage else 0

            # 获取基线GPU内存
            baseline_gpu_mem = self.baseline_snapshot.get('torch_gpu_memory_allocated_mb', 0)

            analysis['total_increment'] = {
                'memory_mb_increase': current_memory_avg - self.baseline_snapshot['memory_used_mb'],
                'gpu_memory_mb_increase': max(0, current_gpu_memory_avg - baseline_gpu_mem)  # 确保非负
            }

        return analysis


class PerformanceBenchmark:
    """性能基准测试主类"""
    
    def __init__(self, model_path: str, test_images_dir: str, max_images: int = 50):
        """
        初始化性能测试
        
        Args:
            model_path: 模型文件路径
            test_images_dir: 测试图片目录
            max_images: 最大测试图片数量
        """
        self.model_path = model_path
        self.test_images_dir = test_images_dir
        self.max_images = max_images
        self.test_images = []
        self.results = {}
        
        # 检查模型文件
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
            
        # 检查测试图片目录
        if not os.path.exists(test_images_dir):
            raise FileNotFoundError(f"测试图片目录不存在: {test_images_dir}")
            
        logger.info(f"初始化性能测试 - 模型: {model_path}, 图片目录: {test_images_dir}")
        
    def load_test_images(self):
        """加载测试图片"""
        logger.info("加载测试图片...")
        
        # 支持的图片格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # 遍历目录查找图片文件
        image_files = []
        for file_path in Path(self.test_images_dir).rglob('*'):
            if file_path.suffix.lower() in image_extensions:
                image_files.append(file_path)
                
        # 限制图片数量
        if len(image_files) > self.max_images:
            image_files = image_files[:self.max_images]
            logger.info(f"限制测试图片数量为 {self.max_images} 张")
            
        # 加载图片
        for image_path in image_files:
            try:
                image = cv2.imread(str(image_path))
                if image is not None:
                    self.test_images.append({
                        'path': str(image_path),
                        'data': image,
                        'name': image_path.name
                    })
                else:
                    logger.warning(f"无法加载图片: {image_path}")
            except Exception as e:
                logger.error(f"加载图片失败 {image_path}: {e}")
                
        logger.info(f"成功加载 {len(self.test_images)} 张测试图片")
        
        if not self.test_images:
            raise ValueError("没有找到可用的测试图片")
            
    def benchmark_device(self, device: str) -> Dict[str, Any]:
        """
        在指定设备上进行基准测试

        Args:
            device: 'cpu' 或 'cuda'

        Returns:
            测试结果字典
        """
        logger.info(f"开始 {device.upper()} 模式性能测试...")

        # 设置设备
        if device == 'cuda' and not torch.cuda.is_available():
            logger.warning("CUDA不可用，跳过GPU测试")
            return {}

        # 初始化资源监控器并记录基线
        monitor = ResourceMonitor()
        monitor.take_baseline_snapshot(device)

        # 使用Ultralytics YOLO直接加载模型，并在推理阶段显式指定device，确保设备隔离
        yolo_device = 'cuda' if device == 'cuda' else 'cpu'
        model = YOLO(self.model_path)

        # 记录模型加载后的资源使用情况（此时模型尚未触发推理，不会自动切换设备）
        monitor.take_model_loaded_snapshot(device)

        # 预热模型（避免首次推理的初始化开销），显式传入device确保CPU/GPU隔离
        logger.info("模型预热中...")
        if self.test_images:
            for _ in range(3):  # 预热3次
                try:
                    _ = model(self.test_images[0]['data'], device=yolo_device, verbose=False)
                except Exception as e:
                    logger.warning(f"预热失败: {e}")

        # 开始监控推理过程
        monitor.start_monitoring()

        # 记录测试开始时间
        start_time = time.time()
        inference_times = []
        successful_inferences = 0
        failed_inferences = 0

        try:
            for i, image_info in enumerate(self.test_images):
                try:
                    # 记录单次推理时间
                    inference_start = time.time()

                    # 执行推理
                    _ = model(image_info['data'], device=yolo_device, verbose=False)

                    inference_end = time.time()
                    inference_time = inference_end - inference_start
                    inference_times.append(inference_time)
                    successful_inferences += 1

                    if (i + 1) % 10 == 0:
                        logger.info(f"已完成 {i + 1}/{len(self.test_images)} 张图片推理")

                except Exception as e:
                    logger.error(f"推理失败 {image_info['name']}: {e}")
                    failed_inferences += 1

        finally:
            # 停止监控
            monitor.stop_monitoring()

        # 记录测试结束时间
        end_time = time.time()
        total_time = end_time - start_time

        # 计算统计信息和增量分析
        resource_stats = monitor.get_stats()
        incremental_analysis = monitor.get_incremental_analysis()

        result = {
            'device': device,
            'total_time': total_time,
            'total_images': len(self.test_images),
            'successful_inferences': successful_inferences,
            'failed_inferences': failed_inferences,
            'inference_times': {
                'avg': statistics.mean(inference_times) if inference_times else 0,
                'max': max(inference_times) if inference_times else 0,
                'min': min(inference_times) if inference_times else 0,
                'median': statistics.median(inference_times) if inference_times else 0,
                'std': statistics.stdev(inference_times) if len(inference_times) > 1 else 0,
                'total': sum(inference_times)
            },
            'throughput': {
                'images_per_second': successful_inferences / total_time if total_time > 0 else 0,
                'ms_per_image': (sum(inference_times) * 1000) / successful_inferences if successful_inferences > 0 else 0
            },
            'resource_usage': resource_stats,
            'incremental_analysis': incremental_analysis
        }

        logger.info(f"{device.upper()} 模式测试完成")
        return result

    def run_benchmark(self) -> Dict[str, Any]:
        """运行完整的基准测试"""
        logger.info("开始性能基准测试")

        # 加载测试图片
        self.load_test_images()

        # 记录系统信息
        system_info = {
            'cpu_count': psutil.cpu_count(),
            'memory_total': psutil.virtual_memory().total / 1024 / 1024 / 1024,  # GB
            'cuda_available': torch.cuda.is_available(),
            'cuda_device_count': torch.cuda.device_count() if torch.cuda.is_available() else 0,
            'cuda_device_name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
            'model_path': self.model_path,
            'test_images_count': len(self.test_images),
            'timestamp': datetime.now().isoformat()
        }

        results = {
            'system_info': system_info,
            'benchmarks': {}
        }

        # CPU测试
        logger.info("=" * 50)
        logger.info("开始CPU性能测试")
        logger.info("=" * 50)
        cpu_result = self.benchmark_device('cpu')
        if cpu_result:
            results['benchmarks']['cpu'] = cpu_result

        # 在CPU和GPU测试之间暂停，等待用户确认继续
        try:
            print("\nCPU测试已完成。按回车键继续执行GPU测试...", flush=True)
            input()
        except EOFError:
            logger.warning("检测到非交互环境，跳过暂停。")

        # GPU测试
        if torch.cuda.is_available():
            logger.info("=" * 50)
            logger.info("开始GPU性能测试")
            logger.info("=" * 50)
            gpu_result = self.benchmark_device('cuda')
            if gpu_result:
                results['benchmarks']['gpu'] = gpu_result
        else:
            logger.warning("CUDA不可用，跳过GPU测试")

        # 生成对比分析
        if 'cpu' in results['benchmarks'] and 'gpu' in results['benchmarks']:
            results['comparison'] = self._generate_comparison(
                results['benchmarks']['cpu'],
                results['benchmarks']['gpu']
            )

        self.results = results
        return results

    def _generate_comparison(self, cpu_result: Dict, gpu_result: Dict) -> Dict[str, Any]:
        """生成CPU vs GPU性能对比分析"""
        comparison = {}

        # 推理时间对比
        cpu_avg_time = cpu_result['inference_times']['avg']
        gpu_avg_time = gpu_result['inference_times']['avg']

        if cpu_avg_time > 0 and gpu_avg_time > 0:
            speedup = cpu_avg_time / gpu_avg_time
            comparison['inference_speedup'] = {
                'cpu_avg_ms': cpu_avg_time * 1000,
                'gpu_avg_ms': gpu_avg_time * 1000,
                'speedup_factor': speedup,
                'improvement_percent': (speedup - 1) * 100
            }

        # 吞吐量对比
        cpu_throughput = cpu_result['throughput']['images_per_second']
        gpu_throughput = gpu_result['throughput']['images_per_second']

        if cpu_throughput > 0 and gpu_throughput > 0:
            throughput_improvement = gpu_throughput / cpu_throughput
            comparison['throughput_improvement'] = {
                'cpu_fps': cpu_throughput,
                'gpu_fps': gpu_throughput,
                'improvement_factor': throughput_improvement,
                'improvement_percent': (throughput_improvement - 1) * 100
            }

        # 资源使用对比
        comparison['resource_usage'] = {
            'cpu': {
                'cpu_usage_avg': cpu_result['resource_usage']['cpu']['avg'],
                'memory_usage_avg': cpu_result['resource_usage']['memory']['avg']
            },
            'gpu': {
                'cpu_usage_avg': gpu_result['resource_usage']['cpu']['avg'],
                'memory_usage_avg': gpu_result['resource_usage']['memory']['avg']
            }
        }

        # 添加GPU特有的资源使用信息
        if 'gpu' in gpu_result['resource_usage']:
            comparison['resource_usage']['gpu'].update({
                'gpu_usage_avg': gpu_result['resource_usage']['gpu']['avg'],
                'gpu_memory_avg': gpu_result['resource_usage']['gpu_memory']['avg']
            })

        return comparison

    def generate_report(self, output_file: Optional[str] = None) -> str:
        """生成详细的性能报告"""
        if not self.results:
            raise ValueError("请先运行基准测试")

        report_lines = []

        # 报告标题
        report_lines.append("=" * 80)
        report_lines.append("模型性能基准测试报告")
        report_lines.append("=" * 80)
        report_lines.append("")

        # 系统信息
        system_info = self.results['system_info']
        report_lines.append("系统信息:")
        report_lines.append(f"  CPU核心数: {system_info['cpu_count']}")
        report_lines.append(f"  内存总量: {system_info['memory_total']:.1f} GB")
        report_lines.append(f"  CUDA可用: {system_info['cuda_available']}")
        if system_info['cuda_available']:
            report_lines.append(f"  GPU设备数: {system_info['cuda_device_count']}")
            report_lines.append(f"  GPU设备名: {system_info['cuda_device_name']}")
        report_lines.append(f"  模型路径: {system_info['model_path']}")
        report_lines.append(f"  测试图片数: {system_info['test_images_count']}")
        report_lines.append(f"  测试时间: {system_info['timestamp']}")
        report_lines.append("")

        # 详细测试结果
        benchmarks = self.results['benchmarks']

        for device, result in benchmarks.items():
            report_lines.append(f"{device.upper()} 模式测试结果:")
            report_lines.append("-" * 40)

            # 基本统计
            report_lines.append(f"  总测试时间: {result['total_time']:.2f} 秒")
            report_lines.append(f"  成功推理数: {result['successful_inferences']}")
            report_lines.append(f"  失败推理数: {result['failed_inferences']}")

            # 推理时间统计
            inference_times = result['inference_times']
            report_lines.append(f"  推理时间统计:")
            report_lines.append(f"    平均: {inference_times['avg']*1000:.2f} ms")
            report_lines.append(f"    最大: {inference_times['max']*1000:.2f} ms")
            report_lines.append(f"    最小: {inference_times['min']*1000:.2f} ms")
            report_lines.append(f"    中位数: {inference_times['median']*1000:.2f} ms")
            report_lines.append(f"    标准差: {inference_times['std']*1000:.2f} ms")

            # 吞吐量
            throughput = result['throughput']
            report_lines.append(f"  吞吐量:")
            report_lines.append(f"    图片/秒: {throughput['images_per_second']:.2f}")
            report_lines.append(f"    毫秒/图片: {throughput['ms_per_image']:.2f}")

            # 资源使用
            resource_usage = result['resource_usage']
            report_lines.append(f"  推理过程资源使用:")
            report_lines.append(f"    CPU使用率: {resource_usage['cpu']['avg']:.1f}% (峰值: {resource_usage['cpu']['max']:.1f}%)")
            # CPU增强指标打印
            if 'cpu_effective_cores' in resource_usage:
                eff = resource_usage['cpu_effective_cores']
                report_lines.append(f"    实际有效核心数: {eff['avg']:.2f} (峰值: {eff['max']:.2f})")
            if 'cpu_active_cores' in resource_usage:
                act = resource_usage['cpu_active_cores']
                report_lines.append(f"    活跃核心数(>{self.monitor.cpu_active_threshold:.0f}%): {act['avg']:.1f} (峰值: {act['max']:.0f})")
            if 'cpu_per_core' in resource_usage:
                per_core_avg = resource_usage['cpu_per_core']['avg']
                preview = ", ".join(f"{v:.1f}%" for v in per_core_avg[:8])
                more = "..." if len(per_core_avg) > 8 else ""
                report_lines.append(f"    每核心平均利用率: [{preview}{(', ' + more) if more else ''}]")

            report_lines.append(f"    内存使用: {resource_usage['memory']['avg']:.1f} MB (峰值: {resource_usage['memory']['max']:.1f} MB)")

            if 'gpu' in resource_usage:
                report_lines.append(f"    GPU使用率: {resource_usage['gpu']['avg']:.1f}% (峰值: {resource_usage['gpu']['max']:.1f}%)")
                report_lines.append(f"    GPU显存: {resource_usage['gpu_memory']['avg']:.1f} MB (峰值: {resource_usage['gpu_memory']['max']:.1f} MB)")

            # 增量分析
            if 'incremental_analysis' in result:
                incremental = result['incremental_analysis']
                report_lines.append(f"  资源使用增量分析:")

                # 基线信息
                if incremental.get('baseline'):
                    baseline = incremental['baseline']
                    report_lines.append(f"    系统基线:")
                    report_lines.append(f"      CPU: {baseline['cpu_percent']:.1f}%, 内存: {baseline['memory_used_mb']:.1f} MB")
                    report_lines.append(f"      GPU: {baseline['gpu_utilization_percent']:.1f}%, 显存: {baseline['gpu_memory_used_mb']:.1f} MB")

                # 模型加载增量
                if incremental.get('model_loading_increment'):
                    model_inc = incremental['model_loading_increment']
                    report_lines.append(f"    模型加载增量:")
                    report_lines.append(f"      内存增加: {model_inc['memory_mb_increase']:.1f} MB")
                    report_lines.append(f"      GPU显存增加: {model_inc['gpu_memory_mb_increase']:.1f} MB")
                    report_lines.append(f"      PyTorch GPU内存增加: {model_inc['torch_gpu_memory_mb_increase']:.1f} MB")

                # 推理过程增量
                if incremental.get('inference_increment'):
                    inf_inc = incremental['inference_increment']
                    report_lines.append(f"    推理过程增量:")
                    report_lines.append(f"      内存增加: {inf_inc['memory_mb_increase']:.1f} MB")
                    report_lines.append(f"      GPU显存增加: {inf_inc['gpu_memory_mb_increase']:.1f} MB")

                # 总增量
                if incremental.get('total_increment'):
                    total_inc = incremental['total_increment']
                    report_lines.append(f"    总增量 (相对基线):")
                    report_lines.append(f"      内存增加: {total_inc['memory_mb_increase']:.1f} MB")
                    report_lines.append(f"      GPU显存增加: {total_inc['gpu_memory_mb_increase']:.1f} MB")

            report_lines.append("")

        # 性能对比分析
        if 'comparison' in self.results:
            comparison = self.results['comparison']
            report_lines.append("CPU vs GPU 性能对比分析:")
            report_lines.append("-" * 40)

            if 'inference_speedup' in comparison:
                speedup = comparison['inference_speedup']
                report_lines.append(f"  推理速度提升:")
                report_lines.append(f"    CPU平均推理时间: {speedup['cpu_avg_ms']:.2f} ms")
                report_lines.append(f"    GPU平均推理时间: {speedup['gpu_avg_ms']:.2f} ms")
                report_lines.append(f"    加速倍数: {speedup['speedup_factor']:.2f}x")
                report_lines.append(f"    性能提升: {speedup['improvement_percent']:.1f}%")

            if 'throughput_improvement' in comparison:
                throughput = comparison['throughput_improvement']
                report_lines.append(f"  吞吐量提升:")
                report_lines.append(f"    CPU吞吐量: {throughput['cpu_fps']:.2f} FPS")
                report_lines.append(f"    GPU吞吐量: {throughput['gpu_fps']:.2f} FPS")
                report_lines.append(f"    提升倍数: {throughput['improvement_factor']:.2f}x")
                report_lines.append(f"    吞吐量提升: {throughput['improvement_percent']:.1f}%")

            report_lines.append("")

        # 结论和建议
        report_lines.append("结论和建议:")
        report_lines.append("-" * 40)

        if 'comparison' in self.results and 'inference_speedup' in self.results['comparison']:
            speedup = self.results['comparison']['inference_speedup']['speedup_factor']
            if speedup > 2:
                report_lines.append(f"  ✓ GPU相比CPU有显著性能提升 ({speedup:.1f}x)，建议在生产环境中使用GPU")
            elif speedup > 1.2:
                report_lines.append(f"  ✓ GPU相比CPU有一定性能提升 ({speedup:.1f}x)，可考虑使用GPU")
            else:
                report_lines.append(f"  ⚠ GPU性能提升不明显 ({speedup:.1f}x)，可能受到其他因素限制")
        else:
            if 'cpu' in benchmarks:
                cpu_fps = benchmarks['cpu']['throughput']['images_per_second']
                if cpu_fps > 10:
                    report_lines.append("  ✓ CPU模式性能良好，适合轻量级部署")
                else:
                    report_lines.append("  ⚠ CPU模式性能较低，建议考虑GPU加速或模型优化")

        report_lines.append("")
        report_lines.append("=" * 80)

        report_text = "\n".join(report_lines)

        # 保存报告到文件
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            logger.info(f"性能报告已保存到: {output_file}")

        return report_text

    def save_results_json(self, output_file: str):
        """保存详细结果到JSON文件"""
        if not self.results:
            raise ValueError("请先运行基准测试")

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        logger.info(f"详细结果已保存到: {output_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='模型性能基准测试工具')
    parser.add_argument('--model', '-m',
                       default='models/best_251018_5types.pt',
                       help='模型文件路径 (默认: models/best_251018_5types.pt)')
    parser.add_argument('--images', '-i',
                       default='app_data/DroneData',
                       help='测试图片目录 (默认: app_data/DroneData)')
    parser.add_argument('--max-images', '-n',
                       type=int, default=50,
                       help='最大测试图片数量 (默认: 50)')
    parser.add_argument('--output-dir', '-o',
                       default='app_data/benchmark_results',
                       help='结果输出目录 (默认: app_data/benchmark_results)')
    parser.add_argument('--verbose', '-v',
                       action='store_true',
                       help='详细输出')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # 创建输出目录
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建基准测试实例
        benchmark = PerformanceBenchmark(
            model_path=args.model,
            test_images_dir=args.images,
            max_images=args.max_images
        )

        # 运行基准测试
        results = benchmark.run_benchmark()

        # 生成报告
        report_file = output_dir / f"performance_report_{timestamp}.txt"
        report = benchmark.generate_report(str(report_file))

        # 保存详细结果
        json_file = output_dir / f"performance_results_{timestamp}.json"
        benchmark.save_results_json(str(json_file))

        # 打印报告摘要
        print("\n" + "=" * 80)
        print("性能测试完成！")
        print("=" * 80)
        print(f"报告文件: {report_file}")
        print(f"详细结果: {json_file}")
        print("\n报告摘要:")
        print("-" * 40)

        # 打印关键指标
        benchmarks = results.get('benchmarks', {})
        for device, result in benchmarks.items():
            fps = result['throughput']['images_per_second']
            avg_time = result['inference_times']['avg'] * 1000
            print(f"{device.upper()}: {fps:.2f} FPS, {avg_time:.2f} ms/图片")

        if 'comparison' in results and 'inference_speedup' in results['comparison']:
            speedup = results['comparison']['inference_speedup']['speedup_factor']
            print(f"GPU加速倍数: {speedup:.2f}x")

        print("\n完整报告请查看输出文件。")

    except Exception as e:
        logger.error(f"基准测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
