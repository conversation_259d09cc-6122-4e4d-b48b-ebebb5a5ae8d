#!/usr/bin/env python3
"""
视频流连接诊断工具

这个工具用于独立测试视频流连接状态，帮助诊断视频流连接问题。
可以在主程序运行期间使用此工具验证是程序问题还是视频流问题。

使用方法:
    python tools/video_stream_diagnostic.py --config config.yaml
    python tools/video_stream_diagnostic.py --config config.yaml --drone 1581F6Q8D24AB00GJ00Q
    python tools/video_stream_diagnostic.py --config config.yaml --monitor --duration 300
"""

import sys
import os
import argparse
import asyncio
import time
import cv2
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import threading
import signal

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class VideoStreamDiagnostic:
    """视频流连接诊断器"""
    
    def __init__(self, config_path: str):
        """
        初始化诊断器
        :param config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.running = False
        self.results = {}
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            print(f"✓ 成功加载配置文件: {self.config_path}")
            return config
        except Exception as e:
            print(f"✗ 加载配置文件失败: {e}")
            sys.exit(1)
    
    def _get_drone_list(self) -> List[Dict[str, str]]:
        """获取无人机列表"""
        drone_list = self.config.get('drone_list', [])
        if not drone_list:
            print("✗ 配置文件中未找到无人机列表")
            return []
        
        # 兼容旧格式（字符串列表）和新格式（对象列表）
        if isinstance(drone_list[0], str):
            return [{'drone_code': code, 'site_name': 'DefaultSite'} for code in drone_list]
        else:
            return drone_list
    
    def _build_rtmp_url(self, drone_code: str) -> str:
        """构建RTMP URL"""
        base_url = self.config['video_stream']['rtmp']['base_url']
        return f"{base_url}{drone_code}-81-0-0"
    
    def _test_single_connection(self, drone_code: str, timeout: int = 10) -> Dict[str, Any]:
        """
        测试单个无人机的视频流连接
        :param drone_code: 无人机编号
        :param timeout: 连接超时时间（秒）
        :return: 测试结果
        """
        rtmp_url = self._build_rtmp_url(drone_code)
        result = {
            'drone_code': drone_code,
            'rtmp_url': rtmp_url,
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'error': None,
            'connection_time': None,
            'frame_count': 0,
            'frame_size': None,
            'fps': None
        }
        
        print(f"  测试连接: {drone_code}")
        print(f"  RTMP URL: {rtmp_url}")
        
        start_time = time.time()
        cap = None
        
        try:
            # 创建VideoCapture对象
            cap = cv2.VideoCapture(rtmp_url, cv2.CAP_FFMPEG)
            
            # 设置超时参数
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout * 1000)
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            
            # 检查是否成功打开
            if not cap.isOpened():
                result['error'] = "VideoCapture无法打开流"
                return result
            
            connection_time = time.time() - start_time
            result['connection_time'] = round(connection_time, 2)
            
            # 尝试读取几帧来验证连接质量
            frame_count = 0
            read_start = time.time()
            
            for i in range(10):  # 尝试读取10帧
                ret, frame = cap.read()
                if ret and frame is not None:
                    frame_count += 1
                    if result['frame_size'] is None:
                        result['frame_size'] = f"{frame.shape[1]}x{frame.shape[0]}"
                else:
                    break
                
                # 避免读取过快
                time.sleep(0.1)
            
            read_time = time.time() - read_start
            result['frame_count'] = frame_count
            
            if frame_count > 0:
                result['fps'] = round(frame_count / read_time, 1)
                result['success'] = True
                print(f"  ✓ 连接成功 - 连接时间: {connection_time:.2f}s, 读取帧数: {frame_count}, FPS: {result['fps']}")
            else:
                result['error'] = "无法读取视频帧"
                print(f"  ✗ 连接失败 - 无法读取视频帧")
                
        except Exception as e:
            result['error'] = str(e)
            print(f"  ✗ 连接失败 - {e}")
            
        finally:
            if cap:
                cap.release()
        
        return result
    
    def test_all_drones(self, timeout: int = 10) -> Dict[str, Any]:
        """
        测试所有无人机的视频流连接
        :param timeout: 连接超时时间（秒）
        :return: 测试结果汇总
        """
        print("=" * 60)
        print("开始视频流连接诊断测试")
        print("=" * 60)
        
        drone_list = self._get_drone_list()
        if not drone_list:
            return {'error': '无可用的无人机列表'}
        
        print(f"发现 {len(drone_list)} 个无人机配置")
        print(f"RTMP基础URL: {self.config['video_stream']['rtmp']['base_url']}")
        print()
        
        results = {
            'test_time': datetime.now().isoformat(),
            'total_drones': len(drone_list),
            'successful_connections': 0,
            'failed_connections': 0,
            'drone_results': []
        }
        
        for drone_info in drone_list:
            drone_code = drone_info['drone_code']
            site_name = drone_info.get('site_name', 'Unknown')
            
            print(f"[{drone_code}] {site_name}")
            
            result = self._test_single_connection(drone_code, timeout)
            results['drone_results'].append(result)
            
            if result['success']:
                results['successful_connections'] += 1
            else:
                results['failed_connections'] += 1
            
            print()
        
        return results
    
    def test_single_drone(self, drone_code: str, timeout: int = 10) -> Dict[str, Any]:
        """
        测试单个无人机的视频流连接
        :param drone_code: 无人机编号
        :param timeout: 连接超时时间（秒）
        :return: 测试结果
        """
        print("=" * 60)
        print(f"测试单个无人机视频流连接: {drone_code}")
        print("=" * 60)
        
        result = self._test_single_connection(drone_code, timeout)
        
        return {
            'test_time': datetime.now().isoformat(),
            'drone_results': [result],
            'successful_connections': 1 if result['success'] else 0,
            'failed_connections': 0 if result['success'] else 1
        }
    
    def monitor_connections(self, duration: int = 300, interval: int = 30) -> None:
        """
        持续监控视频流连接状态
        :param duration: 监控持续时间（秒）
        :param interval: 检查间隔（秒）
        """
        print("=" * 60)
        print(f"开始持续监控视频流连接 - 持续时间: {duration}秒, 检查间隔: {interval}秒")
        print("按 Ctrl+C 停止监控")
        print("=" * 60)
        
        self.running = True
        start_time = time.time()
        check_count = 0
        
        # 设置信号处理器
        def signal_handler(signum, frame):
            print("\n收到停止信号，正在退出...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            while self.running and (time.time() - start_time) < duration:
                check_count += 1
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                print(f"\n[检查 #{check_count}] {current_time}")
                print("-" * 40)
                
                # 快速测试所有无人机（较短超时）
                drone_list = self._get_drone_list()
                success_count = 0
                
                for drone_info in drone_list:
                    drone_code = drone_info['drone_code']
                    result = self._test_single_connection(drone_code, timeout=5)
                    
                    status = "✓" if result['success'] else "✗"
                    error_info = f" ({result['error']})" if result['error'] else ""
                    print(f"  {status} {drone_code}{error_info}")
                    
                    if result['success']:
                        success_count += 1
                
                print(f"  总计: {success_count}/{len(drone_list)} 连接成功")
                
                # 等待下次检查
                if self.running:
                    time.sleep(interval)
                    
        except KeyboardInterrupt:
            print("\n监控已停止")
        
        total_time = time.time() - start_time
        print(f"\n监控完成 - 总时间: {total_time:.1f}秒, 检查次数: {check_count}")
    
    def print_summary(self, results: Dict[str, Any]) -> None:
        """打印测试结果摘要"""
        print("=" * 60)
        print("测试结果摘要")
        print("=" * 60)
        
        if 'error' in results:
            print(f"✗ 测试失败: {results['error']}")
            return
        
        total = results['total_drones']
        success = results['successful_connections']
        failed = results['failed_connections']
        
        print(f"测试时间: {results['test_time']}")
        print(f"总无人机数: {total}")
        print(f"成功连接: {success}")
        print(f"连接失败: {failed}")
        print(f"成功率: {(success/total*100):.1f}%")
        print()
        
        if failed > 0:
            print("失败的连接:")
            for result in results['drone_results']:
                if not result['success']:
                    print(f"  ✗ {result['drone_code']}: {result['error']}")
        
        if success > 0:
            print("\n成功的连接:")
            for result in results['drone_results']:
                if result['success']:
                    conn_time = result['connection_time']
                    frame_count = result['frame_count']
                    fps = result['fps']
                    size = result['frame_size']
                    print(f"  ✓ {result['drone_code']}: {conn_time}s, {frame_count}帧, {fps}fps, {size}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='视频流连接诊断工具')
    parser.add_argument('--config', required=True, help='配置文件路径')
    parser.add_argument('--drone', help='测试指定无人机（可选）')
    parser.add_argument('--monitor', action='store_true', help='持续监控模式')
    parser.add_argument('--duration', type=int, default=300, help='监控持续时间（秒，默认300）')
    parser.add_argument('--interval', type=int, default=30, help='监控检查间隔（秒，默认30）')
    parser.add_argument('--timeout', type=int, default=10, help='连接超时时间（秒，默认10）')
    
    args = parser.parse_args()
    
    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        print(f"✗ 配置文件不存在: {args.config}")
        sys.exit(1)
    
    # 创建诊断器
    diagnostic = VideoStreamDiagnostic(args.config)
    
    try:
        if args.monitor:
            # 持续监控模式
            diagnostic.monitor_connections(args.duration, args.interval)
        elif args.drone:
            # 单个无人机测试
            results = diagnostic.test_single_drone(args.drone, args.timeout)
            diagnostic.print_summary(results)
        else:
            # 测试所有无人机
            results = diagnostic.test_all_drones(args.timeout)
            diagnostic.print_summary(results)
            
    except Exception as e:
        print(f"✗ 诊断过程中发生错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
