#!/usr/bin/env python3
"""
视频流连接诊断工具

这个工具用于独立测试视频流连接状态，帮助诊断视频流连接问题。
可以在主程序运行期间使用此工具验证是程序问题还是视频流问题。

使用方法:
    python tools/video_stream_diagnostic.py --config config.yaml
    python tools/video_stream_diagnostic.py --config config.yaml --drone 1581F6Q8D24AB00GJ00Q
    python tools/video_stream_diagnostic.py --config config.yaml --monitor --duration 300
"""

import sys
import os
import argparse
import time
import cv2
import yaml
from typing import Dict, List, Any
from datetime import datetime
import signal

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 验证路径设置并导入项目模块
try:
    from app.utils.logger import setup_logger
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print(f"📁 项目根目录: {project_root}")
    print(f"📂 当前工作目录: {os.getcwd()}")
    print("\n🔧 解决方案:")
    print("1. 确保在项目根目录下运行此脚本:")
    print("   cd /home/<USER>/softwares/develop/CV")
    print("   python tools/video_stream_diagnostic.py --help")
    print("\n2. 或者使用模块方式运行:")
    print("   python -m tools.video_stream_diagnostic --help")
    print("\n3. 或者设置PYTHONPATH环境变量:")
    print("   export PYTHONPATH=/home/<USER>/softwares/develop/CV:$PYTHONPATH")
    print("   python tools/video_stream_diagnostic.py --help")
    sys.exit(1)

class VideoStreamDiagnostic:
    """视频流连接诊断器"""

    def __init__(self, config_path: str):
        """
        初始化诊断器
        :param config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.running = False
        self.results = {}
        self.active_connections = []  # 跟踪活跃连接，用于优雅退出

        # 设置信号处理器，实现优雅退出
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """信号处理器，实现优雅退出"""
        print(f"\n🛑 收到退出信号 ({signum})，正在优雅退出...")
        self.running = False
        self._cleanup_connections()
        print("✅ 清理完成，程序退出")
        sys.exit(0)

    def _cleanup_connections(self):
        """清理所有活跃的视频连接"""
        for cap in self.active_connections:
            try:
                if cap and cap.isOpened():
                    cap.release()
            except Exception:
                pass
        self.active_connections.clear()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            print(f"✓ 成功加载配置文件: {self.config_path}")
            return config
        except Exception as e:
            print(f"✗ 加载配置文件失败: {e}")
            sys.exit(1)
    
    def _get_drone_list(self) -> List[Dict[str, str]]:
        """获取无人机列表"""
        drone_list = self.config.get('drone_list', [])
        if not drone_list:
            print("✗ 配置文件中未找到无人机列表")
            return []

        # 兼容旧格式（字符串列表）和新格式（对象列表）
        if isinstance(drone_list[0], str):
            return [{'drone_code': code, 'site_name': 'DefaultSite'} for code in drone_list]
        else:
            return drone_list

    def _get_all_drone_codes(self) -> List[str]:
        """获取所有配置的无人机代码列表"""
        drone_list = self._get_drone_list()
        return [drone['drone_code'] for drone in drone_list]
    
    def _build_rtmp_url(self, drone_code: str) -> str:
        """构建RTMP URL，使用与主程序相同的格式"""
        base_url = self.config['video_stream']['rtmp']['base_url']
        # 使用与主程序相同的URL格式：{base_url}{stream_id}-81-0-0
        return f"{base_url}{drone_code}-81-0-0"

    def _get_opencv_connection_params(self) -> Dict[str, Any]:
        """获取与主程序相同的OpenCV连接参数"""
        return {
            'open_timeout': self.config['video_stream'].get('opencv_open_timeout', 30000),  # 30秒
            'read_timeout': self.config['video_stream'].get('opencv_read_timeout', 5000),   # 5秒
            'buffer_size': self.config['video_stream'].get('opencv_buffer_size', 1),       # 缓冲区大小
            'ffmpeg_options': self.config['video_stream'].get('opencv_ffmpeg_capture_options', 'rtsp_transport;tcp')
        }
    
    def _test_single_connection(self, drone_code: str, timeout: int = 10) -> Dict[str, Any]:
        """
        测试单个无人机的视频流连接，使用与主程序相同的连接参数
        :param drone_code: 无人机编号
        :param timeout: 连接超时时间（秒）
        :return: 测试结果
        """
        rtmp_url = self._build_rtmp_url(drone_code)
        opencv_params = self._get_opencv_connection_params()

        result = {
            'drone_code': drone_code,
            'rtmp_url': rtmp_url,
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'error': None,
            'connection_time': None,
            'frame_count': 0,
            'frame_size': None,
            'fps': None,
            'opencv_params': opencv_params
        }

        print(f"  测试连接: {drone_code}")
        print(f"  RTMP URL: {rtmp_url}")
        print(f"  连接参数: 超时={timeout}s, 读取超时={opencv_params['read_timeout']}ms, 缓冲区={opencv_params['buffer_size']}")

        start_time = time.time()
        cap = None

        try:
            # 创建VideoCapture对象，使用与主程序相同的参数
            cap = cv2.VideoCapture(rtmp_url, cv2.CAP_FFMPEG)
            self.active_connections.append(cap)  # 跟踪连接用于优雅退出

            # 设置与主程序相同的超时参数
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout * 1000)
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, opencv_params['read_timeout'])
            cap.set(cv2.CAP_PROP_BUFFERSIZE, opencv_params['buffer_size'])

            # 检查是否成功打开
            if not cap.isOpened():
                result['error'] = "VideoCapture无法打开流"
                return result

            connection_time = time.time() - start_time
            result['connection_time'] = round(connection_time, 2)

            # 尝试读取几帧来验证连接质量
            frame_count = 0
            read_start = time.time()

            for _ in range(10):  # 尝试读取10帧
                if not self.running:  # 检查是否需要退出
                    break

                ret, frame = cap.read()
                if ret and frame is not None:
                    frame_count += 1
                    if result['frame_size'] is None:
                        result['frame_size'] = f"{frame.shape[1]}x{frame.shape[0]}"
                else:
                    break

                # 避免读取过快
                time.sleep(0.1)

            read_time = time.time() - read_start
            result['frame_count'] = frame_count

            if frame_count > 0:
                result['fps'] = round(frame_count / read_time, 1)
                result['success'] = True
                print(f"  ✓ 连接成功 - 连接时间: {connection_time:.2f}s, 读取帧数: {frame_count}, FPS: {result['fps']}")
            else:
                result['error'] = "无法读取视频帧"
                print(f"  ✗ 连接失败 - 无法读取视频帧")

        except KeyboardInterrupt:
            result['error'] = "用户中断"
            print(f"  ⚠ 测试被用户中断")
        except Exception as e:
            result['error'] = str(e)
            print(f"  ✗ 连接失败 - {e}")

        finally:
            if cap:
                cap.release()
                if cap in self.active_connections:
                    self.active_connections.remove(cap)

        return result
    
    def test_all_drones(self, timeout: int = 10) -> Dict[str, Any]:
        """
        测试所有配置无人机的视频流连接
        :param timeout: 连接超时时间（秒）
        :return: 测试结果汇总
        """
        print("=" * 60)
        print("开始视频流连接诊断测试")
        print("=" * 60)

        # 获取所有配置的无人机
        drone_list = self._get_drone_list()
        if not drone_list:
            return {'error': '无可用的无人机列表'}

        drone_codes = [drone['drone_code'] for drone in drone_list]
        print(f"将测试所有 {len(drone_codes)} 个配置的无人机")
        print(f"RTMP基础URL: {self.config['video_stream']['rtmp']['base_url']}")
        print()

        results = {
            'test_time': datetime.now().isoformat(),
            'total_drones': len(drone_codes),
            'successful_connections': 0,
            'failed_connections': 0,
            'drone_results': []
        }

        self.running = True  # 设置运行状态

        for i, drone_code in enumerate(drone_codes, 1):
            if not self.running:  # 检查是否需要退出
                print(f"\n⚠ 测试被中断，已完成 {i-1}/{len(drone_codes)} 个无人机的测试")
                break

            # 获取站点名称（如果可用）
            site_name = "Unknown"
            for drone_info in drone_list:
                if drone_info['drone_code'] == drone_code:
                    site_name = drone_info.get('site_name', 'Unknown')
                    break

            print(f"[{i}/{len(drone_codes)}] [{drone_code}] {site_name}")

            result = self._test_single_connection(drone_code, timeout)
            results['drone_results'].append(result)

            if result['success']:
                results['successful_connections'] += 1
            else:
                results['failed_connections'] += 1

            print()

        return results
    
    def test_single_drone(self, drone_code: str, timeout: int = 10) -> Dict[str, Any]:
        """
        测试单个无人机的视频流连接
        :param drone_code: 无人机编号
        :param timeout: 连接超时时间（秒）
        :return: 测试结果
        """
        print("=" * 60)
        print(f"测试单个无人机视频流连接: {drone_code}")
        print("=" * 60)

        self.running = True  # 设置运行状态
        result = self._test_single_connection(drone_code, timeout)

        return {
            'test_time': datetime.now().isoformat(),
            'total_drones': 1,
            'drone_results': [result],
            'successful_connections': 1 if result['success'] else 0,
            'failed_connections': 0 if result['success'] else 1
        }
    
    def monitor_connections(self, duration: int = 300, interval: int = 30) -> None:
        """
        持续监控视频流连接状态
        :param duration: 监控持续时间（秒）
        :param interval: 检查间隔（秒）
        """
        print("=" * 60)
        print(f"开始持续监控视频流连接 - 持续时间: {duration}秒, 检查间隔: {interval}秒")
        print("监控所有配置的无人机")
        print("按 Ctrl+C 停止监控")
        print("=" * 60)

        self.running = True
        start_time = time.time()
        check_count = 0

        # 获取所有配置的无人机
        drone_list = self._get_drone_list()
        test_drone_codes = [drone['drone_code'] for drone in drone_list]

        if not test_drone_codes:
            print("❌ 没有找到配置的无人机")
            return

        try:
            while self.running and (time.time() - start_time) < duration:
                check_count += 1
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                print(f"\n[检查 #{check_count}] {current_time}")
                print("-" * 40)

                success_count = 0

                for drone_code in test_drone_codes:
                    if not self.running:  # 检查是否需要退出
                        break

                    result = self._test_single_connection(drone_code, timeout=5)

                    status = "✓" if result['success'] else "✗"
                    error_info = f" ({result['error']})" if result['error'] else ""
                    print(f"  {status} {drone_code}{error_info}")

                    if result['success']:
                        success_count += 1

                print(f"  总计: {success_count}/{len(test_drone_codes)} 连接成功")

                # 等待下次检查
                if self.running:
                    for _ in range(interval):
                        if not self.running:
                            break
                        time.sleep(1)

        except KeyboardInterrupt:
            print("\n⚠ 监控被用户中断")

        total_time = time.time() - start_time
        print(f"\n✅ 监控完成 - 总时间: {total_time:.1f}秒, 检查次数: {check_count}")
    
    def print_summary(self, results: Dict[str, Any]) -> None:
        """打印测试结果摘要"""
        print("=" * 60)
        print("测试结果摘要")
        print("=" * 60)
        
        if 'error' in results:
            print(f"✗ 测试失败: {results['error']}")
            return
        
        total = results['total_drones']
        success = results['successful_connections']
        failed = results['failed_connections']
        
        print(f"测试时间: {results['test_time']}")
        print(f"总无人机数: {total}")
        print(f"成功连接: {success}")
        print(f"连接失败: {failed}")
        print(f"成功率: {(success/total*100):.1f}%")
        print()
        
        if failed > 0:
            print("失败的连接:")
            for result in results['drone_results']:
                if not result['success']:
                    print(f"  ✗ {result['drone_code']}: {result['error']}")
        
        if success > 0:
            print("\n成功的连接:")
            for result in results['drone_results']:
                if result['success']:
                    conn_time = result['connection_time']
                    frame_count = result['frame_count']
                    fps = result['fps']
                    size = result['frame_size']
                    print(f"  ✓ {result['drone_code']}: {conn_time}s, {frame_count}帧, {fps}fps, {size}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='视频流连接诊断工具')
    parser.add_argument('--config', required=True, help='配置文件路径')
    parser.add_argument('--drone', help='测试指定无人机（可选）')
    parser.add_argument('--monitor', action='store_true', help='持续监控模式')
    parser.add_argument('--duration', type=int, default=300, help='监控持续时间（秒，默认300）')
    parser.add_argument('--interval', type=int, default=30, help='监控检查间隔（秒，默认30）')
    parser.add_argument('--timeout', type=int, default=10, help='连接超时时间（秒，默认10）')
    args = parser.parse_args()

    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        sys.exit(1)

    # 创建诊断器
    diagnostic = VideoStreamDiagnostic(args.config)
    logger = setup_logger('video_stream_diagnostic')

    try:
        if args.monitor:
            # 持续监控模式
            logger.info("持续监控模式已启动")
            diagnostic.monitor_connections(args.duration, args.interval)
        elif args.drone:
            # 单个无人机测试
            logger.info(f"开始测试无人机: {args.drone}")
            results = diagnostic.test_single_drone(args.drone, args.timeout)
            diagnostic.print_summary(results)
        else:
            # 测试所有配置的无人机
            logger.info("开始测试所有配置的无人机")
            results = diagnostic.test_all_drones(args.timeout)
            diagnostic.print_summary(results)

    except KeyboardInterrupt:
        print("\n🛑 程序被用户中断")
        logger.info("程序被用户中断")
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")
        logger.error(f"诊断过程中发生错误: {e}")
        sys.exit(1)
    finally:
        # 确保清理资源
        diagnostic._cleanup_connections()
        logger.info("诊断工具退出")


if __name__ == '__main__':
    main()
