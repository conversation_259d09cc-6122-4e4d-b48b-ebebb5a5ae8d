#!/usr/bin/env python3
"""
NFS文件同步工具
从远程NFS服务器同步图片文件到本地目录
支持时间筛选和断点续传
"""

import os
import sys
import yaml
import shutil
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import logging
from tqdm import tqdm

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NFSSyncTool:
    """NFS文件同步工具"""
    
    def __init__(self):
        """初始化同步工具"""
        self.main_config = self._load_main_config()
        self.sync_config = self._load_sync_config()
        self.nfs_server = self.main_config['storage']['nfs']['server']
        
        # 硬编码路径配置 - 基于实际NFS导出信息
        self.remote_nfs_path = "/droneData/imageFile"  # NFS路径
        self.remote_smb_path = "droneData/imageFile"   # SMB路径
        self.local_path = Path("app_data/DroneData")   # 本地路径（统一大小写）
        
        self.supported_extensions = set(self.sync_config['sync']['supported_extensions'])
        self.temp_mount_point = None  # 记录临时挂载点，用于清理
        
    def _load_main_config(self) -> Dict:
        """加载主配置文件"""
        config_path = Path("config.yaml")
        if not config_path.exists():
            raise FileNotFoundError("配置文件 config.yaml 不存在")
            
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _load_sync_config(self) -> Dict:
        """加载同步工具配置文件"""
        config_path = Path("config/nfs_sync_config.yaml")
        if not config_path.exists():
            raise FileNotFoundError("同步配置文件 config/nfs_sync_config.yaml 不存在")
            
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _get_time_filter(self) -> tuple[Optional[datetime], Optional[datetime]]:
        """获取时间筛选条件，返回(开始时间, 结束时间)"""
        time_config = self.sync_config['time_filter']
        
        # 检查是否启用交互模式
        if self.sync_config['interactive']['enabled']:
            return self._get_time_filter_interactive()
        
        # 使用配置文件设置
        if not time_config['enabled']:
            logger.info("时间筛选已禁用，将同步所有文件")
            return None, None
        
        start_time_str = time_config.get('start_time')
        end_time_str = time_config.get('end_time')
        
        try:
            # 解析开始时间
            start_time = None
            if start_time_str:
                start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
                logger.info(f"配置开始时间: {start_time}")
            
            # 解析结束时间
            end_time = None
            if end_time_str:
                end_time = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")
                logger.info(f"配置结束时间: {end_time}")
            else:
                end_time = datetime.now()
                logger.info(f"结束时间使用当前时间: {end_time}")
            
            if start_time and end_time and start_time > end_time:
                logger.warning("开始时间晚于结束时间，将交换时间")
                start_time, end_time = end_time, start_time
            
            return start_time, end_time
            
        except ValueError as e:
            logger.error(f"时间格式解析错误: {e}")
            logger.warning("使用默认时间范围：最近3天")
            return datetime.now() - timedelta(days=3), datetime.now()
    
    def _get_time_filter_interactive(self) -> tuple[Optional[datetime], Optional[datetime]]:
        """交互式获取时间筛选条件"""
        print("\n请选择要同步的文件时间范围：")
        print("1. 最近24小时")
        print("2. 最近3天")
        print("3. 最近一周")
        print("4. 最近一个月")
        print("5. 全部文件")
        print("6. 自定义日期")
        
        while True:
            try:
                choice = input("请输入选项(1-6): ").strip()
                
                if choice == "1":
                    return datetime.now() - timedelta(hours=24), datetime.now()
                elif choice == "2":
                    return datetime.now() - timedelta(days=3), datetime.now()
                elif choice == "3":
                    return datetime.now() - timedelta(days=7), datetime.now()
                elif choice == "4":
                    return datetime.now() - timedelta(days=30), datetime.now()
                elif choice == "5":
                    return None, None  # 不筛选时间
                elif choice == "6":
                    date_str = input("请输入日期 (YYYY-MM-DD): ").strip()
                    return datetime.strptime(date_str, "%Y-%m-%d"), datetime.now()
                else:
                    print("无效选项，请重新输入")
                    
            except ValueError as e:
                print(f"日期格式错误: {e}")
            except KeyboardInterrupt:
                print("\n用户取消操作")
                return None, None
    
    def _ensure_local_directory(self):
        """确保本地目录存在"""
        self.local_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"本地目录已准备: {self.local_path}")
    
    def _connect_to_remote(self) -> bool:
        """连接到远程服务器"""
        try:
            # 检查是否为Linux系统
            if os.name == 'posix':
                # 1. 优先尝试NFS挂载
                if self._try_nfs_mount():
                    return True
                    
                # 2. 备选SMB挂载
                if self._try_smb_mount():
                    return True
                    
            else:
                # Windows系统，使用UNC路径
                network_path = f"\\\\{self.nfs_server}\\{self.remote_smb_path}"
                logger.info(f"尝试连接到: {network_path}")
                
                if os.path.exists(network_path):
                    self.remote_network_path = network_path
                    logger.info("远程路径连接成功")
                    return True
            
            logger.error("无法连接到远程服务器，请检查网络连接和挂载状态")
            return False
                
        except Exception as e:
            logger.error(f"连接远程服务器失败: {e}")
            return False
    
    def _try_nfs_mount(self) -> bool:
        """尝试NFS挂载"""
        try:
            # 创建NFS临时挂载点
            nfs_mount_point = "/tmp/nfs_dronedata_mount"
            os.makedirs(nfs_mount_point, exist_ok=True)

            # 定义多种NFS挂载选项，按优先级排序
            mount_options = [
                # 方案1：NFSv4 + 高性能设置
                'ro,soft,timeo=30,vers=4,rsize=1048576,wsize=1048576,sec=sys',
                # 方案2：NFSv3 + 中等性能设置
                'ro,soft,timeo=30,vers=3,rsize=8192,wsize=8192,sec=sys',
                # 方案3：NFSv3 + 无安全验证（如果服务器支持）
                'ro,soft,timeo=30,vers=3,rsize=8192,wsize=8192,sec=none',
                # 方案4：原始设置（兼容性最好）
                'ro,soft,timeo=30'
            ]

            for i, options in enumerate(mount_options, 1):
                logger.info(f"尝试NFS挂载方案{i}: {self.nfs_server}:/droneData -> {nfs_mount_point}")
                logger.debug(f"挂载选项: {options}")

                mount_cmd = [
                    'sudo', 'mount', '-t', 'nfs',
                    f"{self.nfs_server}:/droneData",
                    nfs_mount_point,
                    '-o', options
                ]

                result = subprocess.run(mount_cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    self.temp_mount_point = nfs_mount_point
                    self.remote_network_path = f"{nfs_mount_point}/imageFile"
                    logger.info(f"NFS挂载成功 (使用方案{i})")

                    # 测试挂载点的基本访问权限
                    if self._test_mount_access(nfs_mount_point):
                        logger.info("挂载点访问测试通过")
                        return True
                    else:
                        logger.warning(f"挂载点访问测试失败，尝试下一个方案")
                        # 卸载当前挂载，尝试下一个选项
                        subprocess.run(['sudo', 'umount', nfs_mount_point],
                                     capture_output=True, text=True)
                        continue
                else:
                    logger.debug(f"NFS挂载方案{i}失败: {result.stderr}")
                    continue

            logger.error("所有NFS挂载方案都失败了")
            return False

        except Exception as e:
            logger.error(f"NFS挂载失败: {e}")
            return False

    def _test_mount_access(self, mount_point: str) -> bool:
        """测试挂载点的基本访问权限"""
        try:
            test_path = Path(mount_point) / "imageFile"
            if not test_path.exists():
                logger.warning(f"目标路径不存在: {test_path}")
                return False

            # 尝试列出目录内容（只测试前几个文件）
            file_count = 0
            accessible_count = 0

            for file_path in test_path.iterdir():
                file_count += 1
                if file_count > 10:  # 只测试前10个文件
                    break

                try:
                    if file_path.is_file():
                        # 尝试获取文件stat信息
                        file_path.stat()
                        accessible_count += 1
                except (PermissionError, OSError):
                    # 权限错误是预期的，不影响测试结果
                    pass

            if file_count == 0:
                logger.warning("挂载点目录为空")
                return False

            logger.debug(f"挂载点测试: 检查了{file_count}个文件，{accessible_count}个可访问")
            return True  # 只要能列出目录就认为挂载成功

        except Exception as e:
            logger.warning(f"挂载点访问测试失败: {e}")
            return False

    def _try_smb_mount(self) -> bool:
        """尝试SMB挂载"""
        try:
            # 创建SMB临时挂载点
            smb_mount_point = "/tmp/smb_dronedata_mount"
            os.makedirs(smb_mount_point, exist_ok=True)
            
            # 构建SMB挂载命令
            smb_path = f"//{self.nfs_server}/droneData"
            mount_cmd = [
                'sudo', 'mount', '-t', 'cifs', 
                smb_path, smb_mount_point,
                '-o', 'guest,ro,iocharset=utf8'
            ]
            
            logger.info(f"尝试SMB挂载: {smb_path} -> {smb_mount_point}")
            result = subprocess.run(mount_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.temp_mount_point = smb_mount_point
                self.remote_network_path = f"{smb_mount_point}/imageFile"
                logger.info("SMB挂载成功")
                return True
            else:
                logger.warning(f"SMB挂载失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"SMB挂载失败: {e}")
            return False
    
    def _cleanup_temp_mount(self):
        """清理临时挂载点"""
        if self.temp_mount_point and os.path.exists(self.temp_mount_point):
            try:
                logger.info(f"清理临时挂载点: {self.temp_mount_point}")
                subprocess.run(['sudo', 'umount', self.temp_mount_point], 
                             capture_output=True, text=True)
                os.rmdir(self.temp_mount_point)
                logger.info("临时挂载点清理完成")
            except Exception as e:
                logger.warning(f"清理临时挂载点失败: {e}")
    
    def _get_remote_files(self, start_time: Optional[datetime], end_time: Optional[datetime]) -> List[Dict]:
        """获取远程文件列表"""
        files = []
        total_files_scanned = 0
        permission_denied_count = 0
        extension_filtered_count = 0
        time_filtered_count = 0

        try:
            # 如果直接网络路径不可用，尝试使用本地挂载点进行模拟
            if not hasattr(self, 'remote_network_path'):
                logger.warning("远程路径不可用，使用模拟数据进行测试")
                # 这里可以添加模拟数据用于测试
                return []

            remote_dir = Path(self.remote_network_path)
            if not remote_dir.exists():
                logger.error(f"远程目录不存在: {remote_dir}")
                return []

            logger.info("正在扫描远程文件...")

            for file_path in remote_dir.iterdir():
                try:
                    if file_path.is_file():
                        total_files_scanned += 1

                        # 检查文件扩展名
                        if file_path.suffix.lower() not in self.supported_extensions:
                            extension_filtered_count += 1
                            continue

                        # 获取文件信息 - 添加权限错误处理
                        try:
                            stat = file_path.stat()
                            file_mtime = datetime.fromtimestamp(stat.st_mtime)
                        except PermissionError as pe:
                            permission_denied_count += 1
                            logger.debug(f"权限不足，跳过文件: {file_path.name} - {pe}")
                            continue
                        except OSError as oe:
                            permission_denied_count += 1
                            logger.debug(f"无法访问文件，跳过: {file_path.name} - {oe}")
                            continue

                        # 应用时间筛选
                        if start_time and file_mtime < start_time:
                            time_filtered_count += 1
                            continue
                        if end_time and file_mtime > end_time:
                            time_filtered_count += 1
                            continue

                        files.append({
                            'name': file_path.name,
                            'path': str(file_path),
                            'size': stat.st_size,
                            'mtime': file_mtime
                        })

                except Exception as file_error:
                    # 处理单个文件时的其他错误
                    logger.debug(f"处理文件时出错，跳过: {file_path} - {file_error}")
                    continue

            # 输出详细的扫描统计信息
            logger.info(f"文件扫描完成:")
            logger.info(f"  - 总计扫描文件: {total_files_scanned}")
            logger.info(f"  - 符合条件的文件: {len(files)}")
            if extension_filtered_count > 0:
                logger.info(f"  - 因扩展名过滤: {extension_filtered_count}")
            if time_filtered_count > 0:
                logger.info(f"  - 因时间筛选过滤: {time_filtered_count}")
            if permission_denied_count > 0:
                logger.warning(f"  - 因权限问题跳过: {permission_denied_count}")

            return files

        except Exception as e:
            logger.error(f"获取远程文件列表失败: {e}")
            # 即使出现异常，也返回已收集到的文件列表
            if files:
                logger.info(f"尽管出现错误，仍收集到 {len(files)} 个可访问的文件")
            return files
    
    def _need_sync(self, remote_file: Dict) -> bool:
        """判断文件是否需要同步"""
        local_file_path = self.local_path / remote_file['name']
        
        # 文件不存在，需要同步
        if not local_file_path.exists():
            return True
        
        # 检查文件大小
        local_size = local_file_path.stat().st_size
        remote_size = remote_file['size']
        
        # 大小不同，需要同步
        if local_size != remote_size:
            return True
        
        # 检查修改时间
        local_mtime = datetime.fromtimestamp(local_file_path.stat().st_mtime)
        remote_mtime = remote_file['mtime']
        
        # 远程文件更新，需要同步
        if remote_mtime > local_mtime:
            return True
        
        return False
    
    def _copy_file_with_progress(self, src_path: str, dst_path: Path, file_size: int) -> bool:
        """复制文件"""
        try:
            # 检查是否需要断点续传
            resume_pos = 0
            if dst_path.exists():
                resume_pos = dst_path.stat().st_size
                if resume_pos >= file_size:
                    return True  # 文件已完整
            
            # 复制文件
            with open(src_path, 'rb') as src, open(dst_path, 'ab' if resume_pos > 0 else 'wb') as dst:
                if resume_pos > 0:
                    src.seek(resume_pos)
                
                remaining = file_size - resume_pos
                while remaining > 0:
                    chunk_size = min(8192, remaining)
                    chunk = src.read(chunk_size)
                    if not chunk:
                        break
                    
                    dst.write(chunk)
                    remaining -= len(chunk)
            
            # 验证文件大小
            if dst_path.stat().st_size == file_size:
                return True
            else:
                logger.error(f"文件复制不完整: {dst_path.name}")
                return False
                
        except Exception as e:
            logger.error(f"复制文件失败 {dst_path.name}: {e}")
            return False
    
    def sync(self) -> bool:
        """执行同步操作"""
        try:
            logger.info("开始NFS文件同步...")
            
            # 获取时间筛选条件
            start_time, end_time = self._get_time_filter()
            
            # 检查是否需要确认同步全部文件
            if (start_time is None and end_time is None and 
                self.sync_config['interactive']['confirm_sync_all'] and
                self.sync_config['interactive']['enabled']):
                if input("确认同步全部文件? (y/N): ").lower() != 'y':
                    logger.info("用户取消同步")
                    return False
            
            # 确保本地目录存在
            self._ensure_local_directory()
            
            # 连接远程服务器
            if not self._connect_to_remote():
                logger.error("无法连接到远程服务器")
                return False
            
            # 获取远程文件列表
            remote_files = self._get_remote_files(start_time, end_time)
            if not remote_files:
                logger.info("没有找到需要同步的文件")
                return True
            
            # 筛选需要同步的文件
            files_to_sync = [f for f in remote_files if self._need_sync(f)]
            
            if not files_to_sync:
                logger.info("所有文件都已是最新版本")
                return True
            
            logger.info(f"需要同步 {len(files_to_sync)} 个文件")
            
            # 执行同步
            success_count = 0
            total_size = sum(f['size'] for f in files_to_sync)
            
            logger.info(f"总计需要同步: {total_size / (1024*1024):.2f} MB")
            
            # 添加总体进度条
            with tqdm(total=len(files_to_sync), unit='files', desc="同步进度") as pbar:
                for file_info in files_to_sync:
                    src_path = file_info['path']
                    dst_path = self.local_path / file_info['name']
                    
                    if self._copy_file_with_progress(src_path, dst_path, file_info['size']):
                        success_count += 1
                    else:
                        logger.error(f"同步失败: {file_info['name']}")
                    
                    pbar.update(1)
            
            # 显示同步结果
            logger.info(f"同步完成: {success_count}/{len(files_to_sync)} 个文件成功")
            logger.info(f"文件保存位置: {self.local_path.absolute()}")
            
            return success_count == len(files_to_sync)
            
        except KeyboardInterrupt:
            logger.info("用户中断同步操作")
            return False
        except Exception as e:
            logger.error(f"同步过程出错: {e}")
            return False
        finally:
            # 清理临时挂载点
            self._cleanup_temp_mount()

def main():
    """主函数"""
    try:
        print("=" * 50)
        print("NFS文件同步工具")
        print("=" * 50)
        
        sync_tool = NFSSyncTool()
        success = sync_tool.sync()
        
        if success:
            print("\n同步完成!")
        else:
            print("\n同步失败，请检查日志信息")
            
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        print(f"\n程序运行出错: {e}")

if __name__ == "__main__":
    main() 