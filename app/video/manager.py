import logging
import asyncio
import time
from typing import Dict, Optional, List, Set, Any
from .streams.base import BaseVideoStream
from .streams.rtmp import RTMPStream
from .streams.webrtc import WebRTCStream
from app.database.drone_info import DroneInfoManager
from app.utils.shared_state import active_drones
from app.database.websocket_client import WebSocketClient

logger = logging.getLogger(__name__)

class VideoStreamManager:
    """视频流统一管理器"""
    
    def __init__(self, drone_info_manager: DroneInfoManager):
        """初始化视频流管理器"""
        self.streams: Dict[str, BaseVideoStream] = {}
        self.drone_info_manager = drone_info_manager
        self.websocket_client = None
        self.stream_type = None
        
    def set_websocket_client(self, websocket_client: WebSocketClient):
        """设置WebSocket客户端"""
        self.websocket_client = websocket_client
        
    def _create_stream(self) -> BaseVideoStream:
        """创建视频流实例"""
        if self.stream_type == "webrtc":
            return WebRTCStream()
        return RTMPStream()  # 默认使用RTMP
        
    async def get_stream(self, drone_code: str, stream_type: str = "drone") -> Optional[BaseVideoStream]:
        """
        获取指定无人机的视频流
        :param drone_code: 无人机编号
        :param stream_type: 流类型，"drone"或"airport"
        :return: 视频流对象
        """
        try:
            # 获取无人机信息
            drone_info = self.drone_info_manager.get_drone_info(drone_code)
            if not drone_info:
                logger.warning(f"未找到无人机 {drone_code} 的信息")
                return None
                
            stream_key = f"{drone_code}_{stream_type}"
            if stream_key not in self.streams:
                stream = self._create_stream()
                video_code = (drone_info.drone_video_code
                            if stream_type == "drone"
                            else drone_info.airport_video_code)

                logger.info(f"尝试启动视频流: {drone_code}, 视频编码: {video_code}, 流类型: {stream_type}")
                logger.debug(f"视频流启动详情: stream_key={stream_key}, video_code={video_code}, stream_type={self.stream_type}")

                if await stream.start(video_code):
                    self.streams[stream_key] = stream
                    logger.info(f"成功启动视频流: {drone_code}, 视频编码: {video_code}")

                    # 添加流状态验证
                    if hasattr(stream, 'is_running'):
                        is_running = getattr(stream, 'is_running', False)
                        logger.debug(f"流启动后状态验证: {stream_key}, is_running={is_running}")

                    if hasattr(stream, 'check_stream'):
                        try:
                            is_active = await stream.check_stream()
                            logger.debug(f"流启动后活跃性检查: {stream_key}, is_active={is_active}")
                        except Exception as e:
                            logger.warning(f"流启动后活跃性检查失败: {stream_key}, 错误: {e}")
                else:
                    logger.warning(f"启动视频流失败: {drone_code}, 视频编码: {video_code}")
                    logger.debug(f"流启动失败详情: stream_key={stream_key}, 可能原因: 网络连接问题或视频编码无效")

            else:
                logger.debug(f"复用现有视频流: {stream_key}")

            return self.streams.get(stream_key)
            
        except Exception as e:
            logger.error(f"获取视频流失败: {str(e)}")
            return None
            
    async def cleanup(self):
        """清理所有视频流资源"""
        for stream in self.streams.values():
            await stream.stop()
        self.streams.clear()
        
    async def close_all_streams(self):
        """关闭所有视频流，但保留流对象的引用，以便后续重新初始化"""
        logger.info(f"关闭所有视频流，共 {len(self.streams)} 个")
        for stream_key, stream in self.streams.items():
            try:
                logger.info(f"关闭视频流: {stream_key}")
                await stream.stop()
            except Exception as e:
                logger.error(f"关闭视频流 {stream_key} 时出错: {str(e)}")

        # 清空流字典，但不调用clear()，这样可以保留键值对的引用
        self.streams = {}
        logger.info("所有视频流已关闭")

    def is_stream_active(self, drone_code: str, stream_type: str = "drone") -> bool:
        """
        检查指定无人机的视频流是否活跃
        :param drone_code: 无人机编号
        :param stream_type: 流类型，"drone"或"airport"
        :return: 视频流是否活跃
        """
        stream_key = f"{drone_code}_{stream_type}"

        # 检查流是否存在
        if stream_key not in self.streams:
            logger.debug(f"视频流 {stream_key} 不存在")
            return False

        stream = self.streams[stream_key]

        try:
            # 检查流是否有活跃状态检查方法
            if hasattr(stream, 'is_active'):
                is_active = stream.is_active()
                logger.debug(f"视频流 {stream_key} 活跃状态: {is_active}")
                return is_active
            elif hasattr(stream, 'check_stream'):
                # 如果有 check_stream 方法，可以用来检查流状态
                # 注意：这个方法可能是异步的，这里做同步检查
                logger.debug(f"视频流 {stream_key} 存在但无法同步检查状态，假设为活跃")
                return True
            else:
                # 如果流对象存在但没有状态检查方法，假设为活跃
                logger.debug(f"视频流 {stream_key} 存在但无状态检查方法，假设为活跃")
                return True

        except Exception as e:
            logger.error(f"检查视频流 {stream_key} 状态时出错: {str(e)}")
            return False

    def get_stream_status(self, drone_code: str) -> Dict[str, Any]:
        """
        获取指定无人机的视频流状态信息
        :param drone_code: 无人机编号
        :return: 流状态信息
        """
        drone_stream_key = f"{drone_code}_drone"
        airport_stream_key = f"{drone_code}_airport"

        status = {
            'drone_code': drone_code,
            'drone_stream': {
                'exists': drone_stream_key in self.streams,
                'active': self.is_stream_active(drone_code, "drone")
            },
            'airport_stream': {
                'exists': airport_stream_key in self.streams,
                'active': self.is_stream_active(drone_code, "airport")
            }
        }

        return status

    def get_all_streams_status(self) -> Dict[str, Any]:
        """
        获取所有视频流的状态信息
        :return: 所有流的状态信息
        """
        status = {
            'total_streams': len(self.streams),
            'streams': {},
            'check_time': time.time()
        }

        for stream_key, stream in self.streams.items():
            try:
                # 解析流键获取无人机代码和流类型
                parts = stream_key.split('_')
                if len(parts) >= 2:
                    drone_code = '_'.join(parts[:-1])
                    stream_type = parts[-1]

                    if drone_code not in status['streams']:
                        status['streams'][drone_code] = {}

                    status['streams'][drone_code][stream_type] = {
                        'active': hasattr(stream, 'is_active') and stream.is_active() if hasattr(stream, 'is_active') else True,
                        'stream_key': stream_key
                    }
            except Exception as e:
                logger.error(f"获取流 {stream_key} 状态时出错: {str(e)}")

        return status