from abc import ABC, abstractmethod
from typing import Optional, Any

class BaseVideoStream(ABC):
    """视频流基础接口"""
    
    @abstractmethod
    async def start(self, stream_id: str) -> bool:
        """启动视频流"""
        pass
        
    @abstractmethod
    async def stop(self) -> None:
        """停止视频流"""
        pass
        
    @abstractmethod
    async def get_frame(self) -> Optional[Any]:
        """获取视频帧"""
        pass 