import asyncio
import cv2
import logging
import threading
import time
from typing import Optional, Callable
import numpy as np
import os
import concurrent.futures as cf

from app.utils.config import config
from app.utils.frame_data import FrameData
from app.utils.async_frame_queue import AsyncFrameQueue
from .rtmp_connection_manager import RTMPConnectionManager

logger = logging.getLogger(__name__)

# 如果尚未设置，则设置OpenCV FFMPEG环境变量
if 'OPENCV_FFMPEG_READ_ATTEMPTS' not in os.environ:
    os.environ['OPENCV_FFMPEG_READ_ATTEMPTS'] = str(config.OPENCV_FFMPEG_READ_ATTEMPTS)
if 'OPENCV_FFMPEG_CAPTURE_OPTIONS' not in os.environ:
    # 使用配置中的FFMPEG捕获选项，提高稳定性
    os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = config.OPENCV_FFMPEG_CAPTURE_OPTIONS

class FrameProducer:
    """
    帧生产者，在独立的线程中运行，负责读取视频帧并放入队列。
    此类完全拥有并管理其自己的cv2.VideoCapture对象，以确保线程安全。
    """
    
    def __init__(self, 
                 connection_manager: RTMPConnectionManager, 
                 frame_queue: AsyncFrameQueue,
                 stream_key: str,
                 on_error: Optional[Callable[[Exception], None]] = None):
        """
        初始化帧生产者
        :param connection_manager: 连接管理器，用于获取URL和状态
        :param frame_queue: 用于存放帧的异步队列
        :param stream_key: 流的唯一标识符
        :param on_error: 发生错误时的回调函数
        """
        self.connection_manager = connection_manager
        self.frame_queue = frame_queue
        self.stream_key = stream_key
        self.on_error = on_error
        
        self._running = False
        self._producer_thread: Optional[threading.Thread] = None
        self._loop: Optional[asyncio.AbstractEventLoop] = None
        self._cap: Optional[cv2.VideoCapture] = None
        
        # 统计信息
        self._frame_count = 0
        self._start_time = 0.0
        self._last_stats_time = 0.0
        self._last_frame_time = 0.0
        
        # 从配置加载参数
        self._max_consecutive_failures = config.PRODUCER_MAX_CONSECUTIVE_FAILURES
        self._max_consecutive_empty_frames = config.PRODUCER_MAX_CONSECUTIVE_EMPTY_FRAMES
        self._failure_sleep_time = config.PRODUCER_FAILURE_SLEEP_TIME
        self._empty_frame_sleep_time = config.PRODUCER_EMPTY_FRAME_SLEEP_TIME
        self._stats_interval = config.PRODUCER_STATS_INTERVAL
        self._enqueue_timeout = config.PRODUCER_ENQUEUE_TIMEOUT
        self._frame_read_retry = config.VIDEO_STREAM_FRAME_READ_RETRY
        self._frame_read_retry_delay = config.VIDEO_STREAM_FRAME_READ_RETRY_DELAY

        self._consecutive_failures = 0
        self._consecutive_empty_frames = 0
        self._total_failures = 0
        self._total_empty_frames = 0
        self._successful_reads = 0

    def set_event_loop(self, loop: asyncio.AbstractEventLoop) -> None:
        """设置生产者用于入队的事件循环"""
        self._loop = loop
        
    def start_producing(self) -> bool:
        """启动帧生产线程"""
        if self._running:
            logger.warning(f"帧生产者已在运行: {self.stream_key}")
            return False
        
        logger.info(f"启动帧生产者: {self.stream_key}")
        
        self._running = True
        self.connection_manager.set_active(True)
        self._start_time = time.time()
        
        self._producer_thread = threading.Thread(
            target=self._produce_frames_loop,
            name=f"FrameProducer-{self.stream_key}",
            daemon=True
        )
        self._producer_thread.start()
        
        return True
    
    def stop_producing(self) -> None:
        """停止帧生产线程"""
        if not self._running:
            return
        
        logger.info(f"停止帧生产者: {self.stream_key}")
        self._running = False
        self.connection_manager.set_active(False)
        
        if self._producer_thread and self._producer_thread.is_alive():
            self._producer_thread.join(timeout=5.0)
            if self._producer_thread.is_alive():
                logger.warning(f"生产者线程未能优雅地停止: {self.stream_key}")
        
        self._log_final_stats()
    
    def _produce_frames_loop(self) -> None:
        """生产者线程的主循环"""
        logger.info(f"帧生产循环已启动: {self.stream_key}")
        
        while self._running:
            try:
                # 如果未连接，则尝试连接
                if not self._cap or not self._cap.isOpened():
                    self._connect()
                
                # 如果连接失败，则等待后重试
                if not self._cap:
                    time.sleep(self._failure_sleep_time)
                    continue

                # 读取帧（带重试机制）
                frame = self._read_frame_with_retry()

                if frame is None:
                    self._handle_empty_frame()
                    continue

                self._handle_successful_read(frame)
                
            except Exception as e:
                logger.error(f"生产者循环出现错误 {self.stream_key}: {e}", exc_info=True)
                self._handle_exception(e)
        
        self._disconnect()
        logger.info(f"帧生产循环已结束: {self.stream_key}")

    def _connect(self):
        """创建并配置VideoCapture对象"""
        self._disconnect()  # 确保旧连接已释放
        
        rtmp_url = self.connection_manager.get_url()
        logger.info(f"尝试连接到视频流: {rtmp_url}")
        
        try:
            self._cap = cv2.VideoCapture(rtmp_url, cv2.CAP_FFMPEG)
            self._cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, config.CONNECTION_OPEN_TIMEOUT)
            self._cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, config.CONNECTION_READ_TIMEOUT)
            self._cap.set(cv2.CAP_PROP_BUFFERSIZE, config.CONNECTION_BUFFER_SIZE)
            
            if not self._cap.isOpened():
                raise IOError("VideoCapture未能成功打开")
            
            logger.info(f"已成功连接到视频流: {self.stream_key}")
            self._consecutive_failures = 0
            
        except (cv2.error, IOError, Exception) as e:
            logger.error(f"连接到 {self.stream_key} 失败: {e}")
            self._disconnect()
            self._consecutive_failures += 1

    def _disconnect(self):
        """安全地释放VideoCapture对象"""
        if self._cap:
            try:
                self._cap.release()
                logger.debug(f"VideoCapture已为 {self.stream_key} 释放")
            except Exception as e:
                logger.warning(f"为 {self.stream_key} 释放VideoCapture时出错: {e}")
            finally:
                self._cap = None

    def _read_frame_with_retry(self) -> Optional[np.ndarray]:
        """带重试机制的帧读取"""
        for attempt in range(self._frame_read_retry):
            try:
                if not self._cap or not self._cap.isOpened():
                    return None

                ret, frame = self._cap.read()

                if ret and frame is not None and frame.size > 0:
                    # 成功读取到有效帧
                    return frame
                elif not ret:
                    # 读取失败，这是真正的错误
                    if attempt < self._frame_read_retry - 1:
                        # logger.debug(f"帧读取失败，重试 {attempt + 1}/{self._frame_read_retry}: {self.stream_key}")
                        time.sleep(self._frame_read_retry_delay)
                        continue
                    else:
                        # 所有重试都失败了
                        self._handle_read_failure()
                        return None
                else:
                    # ret=True但frame为空或无效，这是空帧，不是错误
                    return None

            except Exception as e:
                logger.warning(f"读取帧时发生异常 {self.stream_key}: {e}")
                if attempt < self._frame_read_retry - 1:
                    time.sleep(self._frame_read_retry_delay)
                    continue
                else:
                    self._handle_read_failure()
                    return None

        return None

    def _handle_successful_read(self, frame: np.ndarray) -> None:
        """处理成功读取的帧"""
        current_time = time.time()
        self._frame_count += 1
        self._successful_reads += 1
        self._consecutive_failures = 0
        self._consecutive_empty_frames = 0  # 重置空帧计数
        self._last_frame_time = current_time
        
        frame_data = FrameData(
            frame=frame,
            timestamp=current_time * 1000,
            frame_id=self._frame_count,
            metadata={"stream_key": self.stream_key}
        )
        
        self._enqueue_frame(frame_data)
        
        if current_time - self._last_stats_time >= self._stats_interval:
            self._log_stats()
            self._last_stats_time = current_time

    def _enqueue_frame(self, frame_data: FrameData):
        """从当前线程将帧放入异步队列"""
        # 检查生产者是否还在运行
        if not self._running:
            logger.debug(f"生产者已停止，跳过帧入队: {self.stream_key}")
            return

        if self._loop is None:
            logger.error(f"事件循环未设置: {self.stream_key}")
            return

        if self._loop.is_closed():
            logger.debug(f"事件循环已关闭，停止入队: {self.stream_key}")
            self._running = False
            return

        try:
            future = asyncio.run_coroutine_threadsafe(
                self.frame_queue.put_frame(frame_data),
                self._loop
            )
            result = future.result(timeout=self._enqueue_timeout)
            if not result:
                logger.warning(f"帧入队失败（队列可能已关闭）: {self.stream_key}")
                # 如果队列关闭，停止生产者
                self._running = False
            # else:
            #     logger.debug(f"帧入队成功: {self.stream_key}, 帧ID: {frame_data.frame_id}")
        except (asyncio.TimeoutError, cf.TimeoutError):
            # 超时通常表示事件循环短暂繁忙：取消本次入队以丢帧降压
            try:
                future.cancel()
            except Exception:
                pass
            logger.warning(f"帧入队超时，可能事件循环繁忙或队列临时不可用: {self.stream_key}")
            # 入队超时时不立即停止，给一次机会
        except RuntimeError as e:
            if "cannot schedule new futures" in str(e).lower() or "event loop is closed" in str(e).lower():
                logger.debug(f"事件循环已停止，停止生产者: {self.stream_key}")
                self._running = False
            else:
                logger.error(f"帧入队运行时错误 {self.stream_key}: {e}")
        except Exception as e:
            logger.error(f"帧入队时出错 {self.stream_key}: {e}", exc_info=True)

    def _handle_empty_frame(self) -> None:
        """处理空帧（不是错误，只是暂时没有数据）"""
        self._consecutive_empty_frames += 1
        self._total_empty_frames += 1

        # 只在空帧过多时记录警告
        if self._consecutive_empty_frames % 50 == 0:
            logger.debug(
                f"为 {self.stream_key} 连续空帧. "
                f"连续空帧次数: {self._consecutive_empty_frames}"
            )

        if self._consecutive_empty_frames >= self._max_consecutive_empty_frames:
            logger.warning(
                f"为 {self.stream_key} 连续空帧次数过多({self._consecutive_empty_frames}). "
                "可能需要检查视频流状态。"
            )
            # 空帧过多时也尝试重连，但不算作失败
            self._disconnect()

        time.sleep(self._empty_frame_sleep_time)

    def _handle_read_failure(self) -> None:
        """处理帧读取失败（真正的错误）"""
        self._consecutive_failures += 1
        self._total_failures += 1

        logger.warning(
            f"为 {self.stream_key} 读取失败. "
            f"连续失败次数: {self._consecutive_failures}"
        )

        if self._consecutive_failures >= self._max_consecutive_failures:
            logger.error(
                f"为 {self.stream_key} 已达到最大连续读取失败次数({self._consecutive_failures}). "
                "强制进行重连尝试。"
            )
            self._disconnect()  # 强制断开连接，以在下一次循环中触发重连

        time.sleep(self._failure_sleep_time)
    
    def _handle_exception(self, exception: Exception) -> None:
        """处理循环中的意外异常"""
        if self.on_error:
            try:
                self.on_error(exception)
            except Exception as e:
                logger.error(f"on_error回调函数出错: {e}")
        
        self._disconnect()
        time.sleep(self._failure_sleep_time)

    def _log_stats(self) -> None:
        """记录周期性统计信息"""
        elapsed_time = time.time() - self._start_time
        if elapsed_time > 0:
            fps = self._frame_count / elapsed_time
            logger.info(
                f"生产者统计 [{self.stream_key}] - "
                f"FPS: {fps:.2f}, 队列大小: {self.frame_queue.get_queue_size()}, "
                f"成功帧: {self._successful_reads}, 失败: {self._total_failures}, "
                f"空帧: {self._total_empty_frames}, 连续失败: {self._consecutive_failures}, "
                f"连续空帧: {self._consecutive_empty_frames}"
            )
    
    def _log_final_stats(self) -> None:
        """记录停止时的最终统计信息"""
        elapsed_time = time.time() - self._start_time
        if elapsed_time > 0:
            avg_fps = self._frame_count / elapsed_time
            logger.info(f"生产者最终统计 [{self.stream_key}] - 运行时长: {elapsed_time:.1f}s, 总帧数: {self._frame_count}, 平均FPS: {avg_fps:.2f}")

    def is_running(self) -> bool:
        """检查生产者线程是否正在运行"""
        return self._running and self._producer_thread and self._producer_thread.is_alive()

    def get_stats(self) -> dict:
        """获取当前统计信息"""
        elapsed_time = time.time() - self._start_time if self._start_time > 0 else 0
        return {
            "stream_key": self.stream_key,
            "is_running": self.is_running(),
            "frame_count": self._frame_count,
            "successful_reads": self._successful_reads,
            "total_failures": self._total_failures,
            "total_empty_frames": self._total_empty_frames,
            "consecutive_failures": self._consecutive_failures,
            "consecutive_empty_frames": self._consecutive_empty_frames,
            "elapsed_time": elapsed_time,
            "avg_fps": self._frame_count / elapsed_time if elapsed_time > 0 else 0,
            "last_frame_time": self._last_frame_time,
            "queue_size": self.frame_queue.get_queue_size(),
        }