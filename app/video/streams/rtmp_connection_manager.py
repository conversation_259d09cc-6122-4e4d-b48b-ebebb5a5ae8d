import asyncio
import logging
import threading
import time
from typing import Optional

logger = logging.getLogger(__name__)

class RTMPConnectionManager:
    """RTMP连接管理器，仅负责URL和高层连接状态管理"""

    def __init__(self, rtmp_url: str):
        """
        初始化连接管理器
        :param rtmp_url: RTMP流URL
        """
        self.rtmp_url = rtmp_url
        self._is_active = False
        self._lock = threading.Lock()

    def set_active(self, status: bool):
        """设置流的期望状态"""
        with self._lock:
            self._is_active = status
        logger.info(f"RTMP流 '{self.rtmp_url}' 的活动状态设置为 {status}")

    def is_active(self) -> bool:
        """检查流是否被标记为活动"""
        with self._lock:
            return self._is_active

    def get_url(self) -> str:
        """获取RTMP URL"""
        return self.rtmp_url 