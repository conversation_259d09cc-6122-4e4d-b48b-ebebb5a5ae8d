import logging
from typing import Dict, Any, List

# 重新使用现有的共享状态实现来最小化迁移风险
from app.utils.shared_state import shared_state_manager as _legacy_shared_state_manager
from app.utils.shared_state import active_drones as _active_drones

logger = logging.getLogger(__name__)


class StateStore:
    """
    通过迁移到新的SharedStateManager，将旧版状态存储管理器作为 thin wrapper 来实现
    """

    def get_active_drones(self) -> Dict[str, Dict[str, Any]]:
        return _legacy_shared_state_manager.get_active_drones()

    def get_active_drone_ids(self) -> List[str]:
        return _legacy_shared_state_manager.get_active_drone_ids()

    def is_drone_active(self, drone_code: str) -> bool:
        return _legacy_shared_state_manager.is_drone_active(drone_code)

    def update_drone_status(self, drone_code: str, status: Dict[str, Any]):
        return _legacy_shared_state_manager.update_drone_status(drone_code, status)

    def remove_drone_status(self, drone_code: str):
        return _legacy_shared_state_manager.remove_drone_status(drone_code)


# 显式显导出一个单例实例，与现有用法风格一致
state_store = StateStore()

# 兼容性考虑，可选地暴露底层 active_drones 映射
active_drones: Dict[str, Dict[str, Any]] = _active_drones

