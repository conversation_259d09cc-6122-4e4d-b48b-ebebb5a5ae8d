# app/utils/video_utils.py

import cv2
import math
import numpy as np
import logging
from concurrent.futures import ThreadPoolExecutor
from app.processor.image_processor import ImagePreprocessor
from app.utils.config import config

# 在模块级别初始化logger
logger = logging.getLogger('app.utils.video_utils')

def get_video_fps(cap):
    fps = cap.get(cv2.CAP_PROP_FPS)
    if fps == 0:
        fps = 25  # 默认值
    return fps


class VideoProcessor:
    def __init__(self, model_type: str = 'cv'):
        """
        初始化视频处理器
        :param model_type: 可以是所选模型类型
        """
        self.logger = logger
        self.image_processor = ImagePreprocessor(model_type=model_type)
        # 从环境变量读取配置
        self.frame_rate = int(config.FRAME_RATE)
        # 初始化线程池
        self.executor = ThreadPoolExecutor(max_workers=4)

    def process_video(self, video_path: str, target: str, max_duration: int = None, confidence_threshold: float = 0.5) -> dict:
        """
        处理视频，返回检测结果
        :param video_path: 视频文件路径
        :param target: 目标对象
        :param max_duration: 最大处理时间（秒），默认为 None
        :param confidence_threshold: 置信度阈值，默认为0.5
        :return: 包含检测结果的字典
        """
        self.logger.info(f"开始处理视频: {video_path}, 目标: {target}, 最大处理时间: {max_duration} 秒, 置信度阈值: {confidence_threshold}")
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return {"error": "无法打开视频文件"}
        
        try:
            video_fps = get_video_fps(cap)
            frame_interval = math.floor(video_fps / self.frame_rate) if self.frame_rate > 0 else 1

            frame_count = 0
            frame_results = []
            futures = []

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                current_time = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0  # 秒

                # 检查是否超过最大时间范围
                if max_duration and current_time > max_duration:
                    break

                if frame_count % frame_interval == 0:
                    timestamp = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0  # 秒

                    # 提交检测任务到线程池
                    future = self.executor.submit(
                        self._process_frame,
                        frame.copy(),
                        timestamp,
                        target,
                        confidence_threshold
                    )
                    futures.append(future)

                frame_count += 1

            # 收集所有结果
            for future in futures:
                result = future.result()
                if result:
                    frame_results.append(result)

            # 整合视频分析结果
            return self._compile_video_results(video_path, frame_results, target)
        
        except Exception as e:
            self.logger.error(f"视频处理失败: {str(e)}")
            return {"error": str(e)}
        finally:
            cap.release()
            self.executor.shutdown(wait=True)

    def _process_frame(self, frame: np.ndarray, timestamp: float, target: str, confidence_threshold: float = 0.5) -> dict:
        """处理单个视频帧"""
        try:
            self.logger.debug(f"开始处理帧 timestamp={timestamp}, confidence_threshold={confidence_threshold}")
            result = self.image_processor.detect(frame, target, confidence_threshold)
            # self.logger.debug(f"帧处理结果: {result}")
            if result:  # 确保result不为None
                if "error" in result:
                    self.logger.warning(f"帧处理返回错误: {result['error']}")
                    return None
                    
                return {
                    "timestamp": timestamp,
                    "target": target,
                    "count": result.get("count", 0),
                    "detections": result.get("detections", []),
                    "annotated_image": result.get("annotated_image")  # 使用get方法避免KeyError
                }
        except Exception as e:
            self.logger.error(f"帧处理失败 (timestamp={timestamp}): {str(e)}")
        return None

    def _compile_video_results(self, video_path: str, frame_results: list, target: str) -> dict:
        """整合视频分析结果"""
        return {
            "video_path": video_path,
            "frame_rate": self.frame_rate,
            "total_frames": len(frame_results),
            "target": target,
            "detections": [
                {
                    "timestamp": r["timestamp"],
                    "count": r["count"],
                    "detections": r["detections"]
                }
                for r in frame_results if r
            ],
            "annotated_frames": [
                {
                    "timestamp": r["timestamp"],
                    "image": r["annotated_image"]
                }
                for r in frame_results if r
            ]
        }
