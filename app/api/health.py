import time
from fastapi import APIRouter
from app.utils.nfs_manager import nfs_manager

router = APIRouter()

@router.get("/healthz")
def healthz():
    """健康检查端点：返回NFS与运行时间。"""
    try:
        # NFS 状态
        nfs_status = nfs_manager.get_status()
        return {
            "status": "ok",
            "time": time.time(),
            "nfs": nfs_status,
        }
    except Exception as e:
        return {"status": "error", "error": str(e)}

