from typing import List, Dict, Optional
import numpy as np
from app.inference.interfaces import Preprocessor, Detector

class InferencePipeline:
    """组合式推理管道：Preprocessor + Detector（不做IO副作用）"""
    def __init__(self, preprocessor: Preprocessor, detector: Detector):
        self.preprocessor = preprocessor
        self.detector = detector

    def detect_multiple(self, image: np.ndarray, targets: List[Dict]) -> Optional[dict]:
        processed = self.preprocessor.process(image)
        return self.detector.detect_multiple(processed, targets)

