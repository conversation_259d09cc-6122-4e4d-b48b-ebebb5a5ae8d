from typing import List, Dict, Optional
import numpy as np
import logging
from app.models.yolo_model import YOLOModel
from app.utils.config import config

logger = logging.getLogger(__name__)

class YOLODetector:
    """符合 Detector 接口的 YOLO 检测器，复用现有 YOLOModel 行为。"""
    def __init__(self, model_path: Optional[str] = None):
        self.model = YOLOModel(model_path or config.YOLO_MODEL_PATH)

    def detect_multiple(self, image: np.ndarray, targets: List[Dict]) -> Optional[dict]:
        return self.model.detect_multiple(image, targets)

