# -*- coding: utf-8 -*-
"""
诊断用“硬阻塞看门狗”，用于在事件循环被同步阻塞时，主动输出各线程的堆栈，帮助快速定位卡住位置。
仅用于排查，非业务逻辑组件。
"""
from __future__ import annotations

import faulthandler
import sys
import threading
import time


class StallWatchdog:
    """硬阻塞看门狗（基于线程定时检查）。

    用法：
    - 在主流程启动时创建并 start()
    - 事件循环中定期调用 pet()（例如每 1s）
    - 一旦超过超时未被喂狗，则 dump 全部线程堆栈到 stderr（会被日志采集）
    - 调试完成后可 stop()
    """

    def __init__(self, timeout: float = 5.0) -> None:
        self.timeout = timeout
        self._last_pet = time.monotonic()
        self._lock = threading.Lock()
        self._stop_evt = threading.Event()
        self._thread = threading.Thread(target=self._run, name="StallWatchdog", daemon=True)

    def start(self) -> None:
        with self._lock:
            self._last_pet = time.monotonic()
        self._thread.start()

    def pet(self) -> None:
        """喂狗：标记最近一次活动时间。"""
        with self._lock:
            self._last_pet = time.monotonic()

    def stop(self) -> None:
        self._stop_evt.set()
        # 不 join，避免阻塞关闭路径

    def _run(self) -> None:
        # 以较高频率检查，但在触发后延时再允许下一次触发，避免刷屏
        cooldown = 2.0
        next_allowed = 0.0
        while not self._stop_evt.is_set():
            time.sleep(0.2)
            now = time.monotonic()
            with self._lock:
                last = self._last_pet
            if now - last > self.timeout and now >= next_allowed:
                # 输出所有线程堆栈到 stderr
                try:
                    faulthandler.dump_traceback(sys.stderr, all_threads=True)
                except Exception:
                    pass
                # 设置下次允许触发的时间点
                next_allowed = now + cooldown

