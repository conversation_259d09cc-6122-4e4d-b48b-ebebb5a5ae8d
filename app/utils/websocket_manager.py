from fastapi.websockets import WebSocket, WebSocketState
from typing import Dict
import logging
from app.processor.stream_processor import StreamProcessor

logger = logging.getLogger(__name__)

class ConnectionManager:
    def __init__(self):
        """初始化连接管理器"""
        self.active_connections: Dict[str, WebSocket] = {}
        self.processors: Dict[str, StreamProcessor] = {}
        self.logger = logger

    async def connect(self, client_id: str, websocket: WebSocket):
        """建立新的WebSocket连接"""
        try:
            self.logger.info(f"尝试建立WebSocket连接: {client_id}")
            await websocket.accept()
            self.active_connections[client_id] = websocket
            self.logger.info(f"WebSocket连接成功: {client_id}")
        except Exception as e:
            self.logger.error(f"WebSocket连接失败: {client_id}, 错误: {str(e)}")
            raise

    def disconnect(self, client_id: str):
        """断开连接并清理资源"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        if client_id in self.processors:
            self.processors[client_id].stop_processing()
            del self.processors[client_id]
        logger.info(f"客户端 {client_id} 已断开连接")

    async def send_results(self, client_id: str, results: dict):
        """发送处理结果到指定客户端"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.send_json(results)
            except Exception as e:
                logger.error(f"发送结果到客户端 {client_id} 失败: {str(e)}")
                await self.disconnect(client_id)

    def add_processor(self, client_id: str, processor: StreamProcessor):
        """添加新的流处理器"""
        self.processors[client_id] = processor
        logger.debug(f"为客户端 {client_id} 添加了新的处理器")

    def get_processor(self, client_id: str) -> StreamProcessor:
        """获取指定客户端的处理器"""
        return self.processors.get(client_id)

# 创建全局连接管理器实例
manager = ConnectionManager()