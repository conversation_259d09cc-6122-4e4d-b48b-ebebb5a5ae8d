import asyncio
import logging
import time
from typing import Dict, Set, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from app.utils.config import config

logger = logging.getLogger(__name__)

class ComponentState(Enum):
    """组件状态枚举"""
    INITIALIZING = "initializing"
    RUNNING = "running"
    ERROR = "error"
    RECOVERING = "recovering"
    STOPPED = "stopped"

@dataclass
class ComponentStatus:
    """组件状态信息"""
    name: str
    state: ComponentState
    last_check: float
    error_count: int
    last_error: Optional[str] = None
    metadata: Dict = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class ComponentMonitor:
    """组件监控器，监控各组件状态并协调故障恢复"""
    
    def __init__(self):
        """初始化组件监控器"""
        self._components: Dict[str, ComponentStatus] = {}
        self._monitor_task: Optional[asyncio.Task] = None
        self._running = False
        self._check_interval = config.MONITOR_CHECK_INTERVAL
        self._max_error_count = config.MONITOR_MAX_ERROR_COUNT
        self._recovery_callbacks: Dict[str, Callable] = {}
        self._shutdown_callbacks: Dict[str, Callable] = {}
        
    async def start(self):
        """启动监控"""
        if self._running:
            logger.warning("组件监控器已在运行")
            return
            
        self._running = True
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("组件监控器已启动")
    
    async def stop(self):
        """停止监控"""
        if not self._running:
            return
            
        self._running = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
        # 清理所有注册的组件
        self._components.clear()
        self._recovery_callbacks.clear()
        self._shutdown_callbacks.clear()
                
        logger.info("组件监控器已停止，已清理所有组件")
    
    def register_component(self, 
                          component_name: str, 
                          recovery_callback: Optional[Callable] = None,
                          shutdown_callback: Optional[Callable] = None):
        """
        注册组件
        :param component_name: 组件名称
        :param recovery_callback: 恢复回调函数
        :param shutdown_callback: 关闭回调函数
        """
        self._components[component_name] = ComponentStatus(
            name=component_name,
            state=ComponentState.INITIALIZING,
            last_check=time.time(),
            error_count=0
        )
        
        if recovery_callback:
            self._recovery_callbacks[component_name] = recovery_callback
        if shutdown_callback:
            self._shutdown_callbacks[component_name] = shutdown_callback
            
        logger.info(f"注册组件: {component_name}")
    
    def unregister_component(self, component_name: str):
        """注销组件"""
        if component_name in self._components:
            del self._components[component_name]
        if component_name in self._recovery_callbacks:
            del self._recovery_callbacks[component_name]
        if component_name in self._shutdown_callbacks:
            del self._shutdown_callbacks[component_name]
            
        logger.info(f"注销组件: {component_name}")
    
    def update_component_state(self, 
                              component_name: str, 
                              state: ComponentState,
                              metadata: Optional[Dict] = None,
                              error_message: Optional[str] = None):
        """
        更新组件状态
        :param component_name: 组件名称
        :param state: 新状态
        :param metadata: 元数据
        :param error_message: 错误消息
        """
        if component_name not in self._components:
            logger.warning(f"尝试更新未注册的组件状态: {component_name}")
            return
        
        component = self._components[component_name]
        old_state = component.state
        component.state = state
        component.last_check = time.time()
        
        if metadata:
            component.metadata.update(metadata)
            
        if error_message:
            component.last_error = error_message
            if state == ComponentState.ERROR:
                component.error_count += 1
        elif state == ComponentState.RUNNING:
            # 运行正常时重置错误计数
            component.error_count = 0
            component.last_error = None
        
        # 记录状态变化
        if old_state != state:
            logger.info(f"组件状态变化: {component_name} {old_state.value} -> {state.value}")
            
            # 如果状态变为错误，触发故障处理
            if state == ComponentState.ERROR:
                asyncio.create_task(self._handle_component_failure(component_name))
    
    async def _monitor_loop(self):
        """监控循环"""
        while self._running:
            try:
                await self._check_all_components()
                await asyncio.sleep(self._check_interval)
            except Exception as e:
                logger.error(f"监控循环出错: {str(e)}")
                await asyncio.sleep(5.0)  # 出错时等待更短时间
    
    async def _check_all_components(self):
        """检查所有组件状态"""
        current_time = time.time()
        
        for component_name, component in self._components.items():
            try:
                # 检查组件是否长时间未更新状态
                if current_time - component.last_check > self._check_interval * 2:
                    logger.warning(f"组件长时间未更新状态: {component_name}, "
                                 f"最后更新: {current_time - component.last_check:.1f}秒前")
                    
                    # 如果组件处于运行状态但长时间未更新，可能出现问题
                    if component.state == ComponentState.RUNNING:
                        await self._handle_component_timeout(component_name)
                
                # 检查错误组件是否需要恢复
                if (component.state == ComponentState.ERROR and 
                    component.error_count < self._max_error_count):
                    await self._attempt_recovery(component_name)
                    
            except Exception as e:
                logger.error(f"检查组件状态时出错: {component_name}, 错误: {str(e)}")
    
    async def _handle_component_failure(self, component_name: str):
        """
        处理组件故障
        :param component_name: 组件名称
        """
        component = self._components.get(component_name)
        if not component:
            return
        
        logger.error(f"组件故障: {component_name}, 错误次数: {component.error_count}")
        
        # 如果错误次数超过阈值，关闭组件
        if component.error_count >= self._max_error_count:
            await self._shutdown_component(component_name)
        else:
            # 尝试恢复
            await self._attempt_recovery(component_name)
    
    async def _handle_component_timeout(self, component_name: str):
        """
        处理组件超时
        :param component_name: 组件名称
        """
        logger.warning(f"组件响应超时: {component_name}")
        
        # 将状态设置为错误
        self.update_component_state(
            component_name, 
            ComponentState.ERROR,
            error_message="组件响应超时"
        )
    
    async def _attempt_recovery(self, component_name: str):
        """
        尝试恢复组件
        :param component_name: 组件名称
        """
        component = self._components.get(component_name)
        if not component:
            return
        
        # 设置恢复状态
        component.state = ComponentState.RECOVERING
        logger.info(f"尝试恢复组件: {component_name}")
        
        try:
            # 调用恢复回调
            if component_name in self._recovery_callbacks:
                recovery_callback = self._recovery_callbacks[component_name]
                success = await recovery_callback()
                
                if success:
                    logger.info(f"组件恢复成功: {component_name}")
                    self.update_component_state(component_name, ComponentState.RUNNING)
                else:
                    logger.error(f"组件恢复失败: {component_name}")
                    self.update_component_state(
                        component_name, 
                        ComponentState.ERROR,
                        error_message="恢复失败"
                    )
            else:
                logger.warning(f"组件没有恢复回调函数: {component_name}")
                
        except Exception as e:
            logger.error(f"恢复组件时出错: {component_name}, 错误: {str(e)}")
            self.update_component_state(
                component_name, 
                ComponentState.ERROR,
                error_message=f"恢复异常: {str(e)}"
            )
    
    async def _shutdown_component(self, component_name: str):
        """
        关闭组件
        :param component_name: 组件名称
        """
        component = self._components.get(component_name)
        if not component:
            return
        
        logger.error(f"关闭故障组件: {component_name}")
        
        try:
            # 调用关闭回调
            if component_name in self._shutdown_callbacks:
                shutdown_callback = self._shutdown_callbacks[component_name]
                await shutdown_callback()
            
            # 设置停止状态
            component.state = ComponentState.STOPPED
            logger.info(f"组件已关闭: {component_name}")
            
        except Exception as e:
            logger.error(f"关闭组件时出错: {component_name}, 错误: {str(e)}")
    
    def get_component_status(self, component_name: str) -> Optional[ComponentStatus]:
        """获取组件状态"""
        return self._components.get(component_name)
    
    def get_all_statuses(self) -> Dict[str, ComponentStatus]:
        """获取所有组件状态"""
        return self._components.copy()
    
    def get_healthy_components(self) -> Set[str]:
        """获取健康组件列表"""
        return {
            name for name, component in self._components.items()
            if component.state == ComponentState.RUNNING
        }
    
    def get_failed_components(self) -> Set[str]:
        """获取故障组件列表"""
        return {
            name for name, component in self._components.items()
            if component.state in [ComponentState.ERROR, ComponentState.STOPPED]
        }
    
    def is_component_healthy(self, component_name: str) -> bool:
        """检查组件是否健康"""
        component = self._components.get(component_name)
        return component is not None and component.state == ComponentState.RUNNING
    
    def get_monitor_stats(self) -> Dict:
        """获取监控统计信息"""
        total = len(self._components)
        running = len([c for c in self._components.values() if c.state == ComponentState.RUNNING])
        error = len([c for c in self._components.values() if c.state == ComponentState.ERROR])
        stopped = len([c for c in self._components.values() if c.state == ComponentState.STOPPED])
        
        return {
            "total_components": total,
            "running": running,
            "error": error,
            "stopped": stopped,
            "health_rate": (running / total * 100) if total > 0 else 0
        } 