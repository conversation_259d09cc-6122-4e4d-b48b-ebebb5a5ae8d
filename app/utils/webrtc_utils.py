import logging
from typing import Optional
from aiortc import MediaStreamTrack, RTCPeerConnection, RTCSessionDescription
from aiortc.mediastreams import MediaStreamError
from aiortc.rtcconfiguration import RTCConfiguration, RTCIceServer

logger = logging.getLogger(__name__)

class VideoStreamTrack(MediaStreamTrack):
    """处理视频流的轨道"""
    kind = "video"

    def __init__(self):
        super().__init__()
        self._track = None
        self.frame_count = 0
        # 设置轨道方向
        self._direction = "recvonly"  # 服务器只接收，不发送

    async def recv(self):
        """接收视频帧"""
        try:
            if self._track:
                frame = await self._track.recv()
                self.frame_count += 1
                return frame
            raise MediaStreamError("No track available")
            
        except Exception as e:
            logger.error(f"视频帧接收错误: {str(e)}")
            raise MediaStreamError("视频帧接收失败")

class WebRTCProcessor:
    """WebRTC流处理器"""
    def __init__(self):
        self.logger = logger
        self.is_running = False
        self.pc: Optional[RTCPeerConnection] = None
        self.video_track: Optional[VideoStreamTrack] = None

    async def start(self, offer: str):
        """启动WebRTC流处理"""
        try:
            self.logger.info("开始启动WebRTC流处理...")
            
            if self.is_running:
                self.logger.warning("流处理器已经在运行")
                return None
                
            # 创建正确格式的RTCConfiguration
            config = RTCConfiguration(
                iceServers=[
                    RTCIceServer(urls=["stun:stun.l.google.com:19302"])
                ]
            )
            
            # 使用正确的配置创建PeerConnection
            self.pc = RTCPeerConnection(configuration=config)
            self.logger.info("已创建RTCPeerConnection")
            
            @self.pc.on("track")
            async def on_track(track):
                self.logger.info(f"收到轨道: {track.kind}")
                if track.kind == "video":
                    self.video_track = track
            
            # 记录offer SDP用于调试
            self.logger.info(f"Received offer SDP: {offer[:200]}...")
            
            offer_desc = RTCSessionDescription(sdp=offer, type="offer")
            await self.pc.setRemoteDescription(offer_desc)
            
            answer = await self.pc.createAnswer()
            self.logger.info(f"Created answer SDP: {answer.sdp[:200]}...")
            
            await self.pc.setLocalDescription(answer)
            self.logger.info("本地描述设置完成")
            
            self.is_running = True
            return answer
            
        except Exception as e:
            self.logger.error(f"启动WebRTC流失败: {str(e)}")
            self.logger.exception("详细错误信息:")
            await self.cleanup()
            return None

    async def cleanup(self):
        """清理资源"""
        try:
            if self.video_track:
                self.video_track.stop()
                self.video_track = None
                
            if self.pc:
                await self.pc.close()
                self.pc = None
                
            self.is_running = False
            
        except Exception as e:
            self.logger.error(f"清理资源失败: {str(e)}")

    async def stop(self):
        """停止WebRTC流处理"""
        await self.cleanup() 