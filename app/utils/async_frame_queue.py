import asyncio
import logging
import time
from typing import Optional
from collections import deque
from .frame_data import FrameData, FrameStats

logger = logging.getLogger(__name__)

class AsyncFrameQueue:
    """异步帧队列，支持生产者-消费者模式"""
    
    def __init__(self, max_size: int = 100, drop_old_frames: bool = True):
        """
        初始化异步帧队列
        :param max_size: 队列最大大小
        :param drop_old_frames: 队列满时是否丢弃旧帧
        """
        self.max_size = max_size
        self.drop_old_frames = drop_old_frames
        self._queue = deque(maxlen=max_size if drop_old_frames else None)
        self._not_empty = asyncio.Condition()
        self._stats = FrameStats()
        self._closed = False
        
    async def put_frame(self, frame_data: FrameData) -> bool:
        """
        放入帧到队列
        :param frame_data: 帧数据
        :return: 是否成功放入
        """
        if self._closed:
            logger.debug("队列已关闭，无法放入帧")
            return False

        try:
            async with self._not_empty:
                # 检查队列是否已满
                if not self.drop_old_frames and len(self._queue) >= self.max_size:
                    logger.warning(f"帧队列已满，丢弃帧 {frame_data.frame_id}")
                    self._stats.update_queue_stats(len(self._queue), 1)
                    return False

                # 如果启用了丢弃旧帧，deque会自动处理
                old_size = len(self._queue)
                self._queue.append(frame_data)
                new_size = len(self._queue)

                # 更新统计信息
                dropped = max(0, old_size + 1 - new_size) if old_size > 0 else 0
                self._stats.update_queue_stats(new_size, dropped)

                # 通知等待的消费者
                self._not_empty.notify()

                # logger.debug(f"帧入队成功，队列大小: {new_size}, 帧ID: {frame_data.frame_id}")
                return True

        except Exception as e:
            logger.error(f"帧入队时发生异常: {str(e)}", exc_info=True)
            return False
    
    async def get_frame(self, timeout: float = 1.0) -> Optional[FrameData]:
        """
        从队列获取帧
        :param timeout: 超时时间（秒）
        :return: 帧数据或None
        """
        if self._closed:
            return None
            
        try:
            async with self._not_empty:
                # 等待有帧可用
                if not self._queue:
                    await asyncio.wait_for(
                        self._not_empty.wait(),
                        timeout=timeout
                    )
                
                # 再次检查队列（可能在等待期间被其他消费者消费）
                if self._queue:
                    frame_data = self._queue.popleft()
                    self._stats.update_queue_stats(len(self._queue))
                    return frame_data
                    
                return None
                
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            logger.error(f"从帧队列获取帧时出错: {str(e)}")
            return None
    
    async def get_latest_frame(self) -> Optional[FrameData]:
        """
        获取最新的帧（丢弃队列中的其他帧）
        :return: 最新帧数据或None
        """
        if self._closed:
            return None
            
        async with self._not_empty:
            if not self._queue:
                return None
            
            # 获取最新帧，丢弃其他帧
            latest_frame = self._queue[-1]
            dropped_count = len(self._queue) - 1
            self._queue.clear()
            
            # 更新统计信息
            self._stats.update_queue_stats(0, dropped_count)
            
            return latest_frame
    
    def clear(self) -> int:
        """
        清空队列
        :return: 清空的帧数
        """
        cleared_count = len(self._queue)
        self._queue.clear()
        self._stats.update_queue_stats(0, cleared_count)
        return cleared_count
    
    def get_queue_size(self) -> int:
        """获取当前队列大小"""
        return len(self._queue)
    
    def get_stats(self) -> FrameStats:
        """获取统计信息"""
        return self._stats
    
    def is_full(self) -> bool:
        """检查队列是否已满"""
        return len(self._queue) >= self.max_size
    
    def is_empty(self) -> bool:
        """检查队列是否为空"""
        return len(self._queue) == 0
    
    async def close(self):
        """关闭队列"""
        self._closed = True
        async with self._not_empty:
            self._not_empty.notify_all()
    
    def __len__(self) -> int:
        """队列长度"""
        return len(self._queue) 