import os
import shutil
import logging
from datetime import datetime
from typing import List, Dict, Optional
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.utils.config import config
from app.utils.nfs_manager import nfs_manager

logger = logging.getLogger(__name__)

class FileTransferManager:
    """文件转移管理器，负责将临时文件转移到挂载点"""
    
    def __init__(self):
        """初始化文件转移管理器"""
        self.temp_dirs = {
            'images': config.TEMP_IMAGE_DIR,
            'videos': config.TEMP_VIDEO_DIR,
            'reports': config.TEMP_REPORT_DIR
        }
        self.mount_points = {
            'images': config.IMAGE_MOUNT_POINT,
            'videos': config.VIDEO_MOUNT_POINT,
            'reports': config.REPORTS_MOUNT_POINT
        }
        
    def _get_date_path(self, timestamp: float) -> str:
        """
        根据时间戳获取日期路径
        :param timestamp: 时间戳（毫秒）
        :return: 日期路径（YYYYMMDD）
        """
        dt = datetime.fromtimestamp(timestamp / 1000.0)
        return dt.strftime('%Y%m%d')
    
    def _ensure_mount_dir_exists(self, file_type: str, timestamp: float) -> Optional[str]:
        """
        确保挂载点目录存在
        :param file_type: 文件类型（'images'/'videos'/'reports'）
        :param timestamp: 时间戳（毫秒）
        :return: 挂载点目录路径，失败则返回None
        """
        if file_type not in self.mount_points:
            logger.error(f"未知的文件类型: {file_type}")
            return None
            
        try:
            # 获取挂载点基础目录
            base_dir = self.mount_points[file_type]
            if not base_dir:
                logger.error(f"未配置挂载点: {file_type}")
                return None
                
            # 创建日期目录
            date_path = self._get_date_path(timestamp)
            full_dir = os.path.join(base_dir, date_path)
            os.makedirs(full_dir, exist_ok=True)
            
            logger.debug(f"确保挂载点目录存在: {full_dir}")
            return full_dir
            
        except Exception as e:
            logger.error(f"创建挂载点目录失败: {str(e)}")
            return None
    
    def transfer_files_by_date(self, file_type: str, date_str: str) -> Dict[str, int]:
        """
        按日期转移文件
        :param file_type: 文件类型（'images'/'videos'/'reports'）
        :param date_str: 日期字符串（YYYYMMDD）
        :return: 转移结果统计
        """
        if file_type not in self.temp_dirs:
            logger.error(f"未知的文件类型: {file_type}")
            return {'success': 0, 'failed': 0, 'skipped': 0}
            
        temp_dir = self.temp_dirs[file_type]
        mount_dir = self.mount_points[file_type]
        
        if not mount_dir:
            logger.error(f"未配置挂载点: {file_type}")
            return {'success': 0, 'failed': 0, 'skipped': 0}
            
        # 构建源目录和目标目录
        source_dir = os.path.join(temp_dir, date_str)
        target_dir = os.path.join(mount_dir, date_str)
        
        if not os.path.exists(source_dir):
            logger.info(f"源目录不存在: {source_dir}")
            return {'success': 0, 'failed': 0, 'skipped': 0}
            
        # 确保目标目录存在
        os.makedirs(target_dir, exist_ok=True)
        
        stats = {'success': 0, 'failed': 0, 'skipped': 0}
        
        try:
            # 遍历源目录中的所有文件
            for filename in os.listdir(source_dir):
                source_file = os.path.join(source_dir, filename)
                target_file = os.path.join(target_dir, filename)
                
                if os.path.isfile(source_file):
                    try:
                        # 检查目标文件是否已存在
                        if os.path.exists(target_file):
                            # 比较文件大小，如果相同则跳过
                            if os.path.getsize(source_file) == os.path.getsize(target_file):
                                logger.debug(f"文件已存在且大小相同，跳过: {filename}")
                                stats['skipped'] += 1
                                continue
                        
                        # 复制文件
                        shutil.copy2(source_file, target_file)
                        
                        # 验证复制是否成功
                        if os.path.exists(target_file) and os.path.getsize(source_file) == os.path.getsize(target_file):
                            # 删除源文件
                            os.remove(source_file)
                            logger.debug(f"文件转移成功: {filename}")
                            stats['success'] += 1
                        else:
                            logger.error(f"文件转移验证失败: {filename}")
                            stats['failed'] += 1
                            
                    except Exception as e:
                        logger.error(f"转移文件失败: {filename}, 错误: {str(e)}")
                        stats['failed'] += 1
            
            # 如果源目录为空，删除源目录
            if not os.listdir(source_dir):
                os.rmdir(source_dir)
                logger.debug(f"删除空目录: {source_dir}")
                
        except Exception as e:
            logger.error(f"转移文件时出错: {str(e)}")
            
        logger.info(f"文件转移完成 - 类型: {file_type}, 日期: {date_str}, 成功: {stats['success']}, 失败: {stats['failed']}, 跳过: {stats['skipped']}")
        return stats
    
    def transfer_all_temp_files(self, drone_code: str = None) -> Dict[str, Dict[str, int]]:
        """
        转移所有临时文件
        :param drone_code: 无人机编号（可选，用于过滤特定无人机的文件）
        :return: 转移结果统计
        """
        logger.info("开始转移所有临时文件")
        
        # 检查NFS挂载状态
        if not nfs_manager.check_and_remount():
            logger.error("NFS挂载检查失败，无法转移文件")
            return {}
        
        results = {}
        
        for file_type in self.temp_dirs.keys():
            temp_dir = self.temp_dirs[file_type]
            
            if not os.path.exists(temp_dir):
                logger.debug(f"临时目录不存在: {temp_dir}")
                continue
                
            # 遍历临时目录中的所有日期目录
            for date_dir in os.listdir(temp_dir):
                date_path = os.path.join(temp_dir, date_dir)
                
                if not os.path.isdir(date_path):
                    continue
                    
                # 如果指定了无人机编号，检查目录中是否有该无人机的文件
                if drone_code:
                    has_drone_files = False
                    for filename in os.listdir(date_path):
                        if filename.startswith(f"{drone_code}_"):
                            has_drone_files = True
                            break
                    
                    if not has_drone_files:
                        continue
                
                # 转移该日期的文件
                stats = self.transfer_files_by_date(file_type, date_dir)
                if stats['success'] > 0 or stats['failed'] > 0 or stats['skipped'] > 0:
                    results[f"{file_type}_{date_dir}"] = stats
        
        total_success = sum(stats['success'] for stats in results.values())
        total_failed = sum(stats['failed'] for stats in results.values())
        total_skipped = sum(stats['skipped'] for stats in results.values())
        
        logger.info(f"文件转移完成 - 总计成功: {total_success}, 失败: {total_failed}, 跳过: {total_skipped}")
        return results
    
    def transfer_drone_files(self, drone_code: str) -> Dict[str, Dict[str, int]]:
        """
        转移特定无人机的文件
        :param drone_code: 无人机编号
        :return: 转移结果统计
        """
        logger.info(f"开始转移无人机 {drone_code} 的文件")
        return self.transfer_all_temp_files(drone_code)
    
    async def transfer_drone_files_async(self, drone_code: str) -> Dict[str, Dict[str, int]]:
        """
        异步转移特定无人机的文件
        :param drone_code: 无人机编号
        :return: 转移结果统计
        """
        logger.info(f"开始异步转移无人机 {drone_code} 的文件")
        # 在线程池中执行同步操作
        import asyncio
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.transfer_drone_files, drone_code)

# 创建全局文件转移管理器实例
file_transfer_manager = FileTransferManager()

# 如果直接运行此文件，执行基本测试
if __name__ == "__main__":
    print("文件转移管理器初始化测试")
    print("=" * 50)
    
    # 测试配置加载
    print(f"临时图片目录: {file_transfer_manager.temp_dirs['images']}")
    print(f"挂载点图片目录: {file_transfer_manager.mount_points['images']}")
    print(f"临时视频目录: {file_transfer_manager.temp_dirs['videos']}")
    print(f"挂载点视频目录: {file_transfer_manager.mount_points['videos']}")
    print(f"临时报告目录: {file_transfer_manager.temp_dirs['reports']}")
    print(f"挂载点报告目录: {file_transfer_manager.mount_points['reports']}")
    
    # 测试目录是否存在
    print("\n目录存在性检查:")
    for file_type, temp_dir in file_transfer_manager.temp_dirs.items():
        exists = os.path.exists(temp_dir)
        print(f"  {file_type} 临时目录: {'✅ 存在' if exists else '❌ 不存在'} - {temp_dir}")
    
    for file_type, mount_dir in file_transfer_manager.mount_points.items():
        exists = os.path.exists(mount_dir)
        print(f"  {file_type} 挂载目录: {'✅ 存在' if exists else '❌ 不存在'} - {mount_dir}")
    
    print("\n文件转移管理器初始化完成") 