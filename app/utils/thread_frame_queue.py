import threading
import time
from collections import deque
from typing import Optional

from app.utils.frame_data import FrameData


class ThreadFrameQueue:
    """线程安全的帧队列，支持阻塞式获取与背压（丢旧帧/拒绝新帧）。

    - 使用 deque 存储，结合 Condition 实现 put/get 的线程安全与等待/唤醒。
    - 可配置最大容量与满时策略：
      * drop_old_frames=True: 队列满时丢弃最旧帧，永远接收新帧（跳帧）。
      * drop_old_frames=False: 队列满时拒绝新帧（返回 False）。
    """

    def __init__(self, max_size: int = 100, drop_old_frames: bool = True):
        self.max_size = max_size
        self.drop_old_frames = drop_old_frames
        self._queue: deque[FrameData] = deque()
        self._cv = threading.Condition()
        self._closed = False

    def put(self, frame_data: FrameData) -> bool:
        """放入帧（非阻塞）。满时根据策略处理。
        返回是否成功入队。"""
        with self._cv:
            if self._closed:
                return False

            if len(self._queue) >= self.max_size:
                if self.drop_old_frames and len(self._queue) > 0:
                    # 丢弃最旧帧，腾出空间接纳新帧，实现跳帧策略
                    self._queue.popleft()
                else:
                    # 拒绝新帧
                    return False

            self._queue.append(frame_data)
            self._cv.notify()
            return True

    def get(self, timeout: float = 1.0) -> Optional[FrameData]:
        """阻塞式获取一帧，超时返回 None。"""
        end_time = time.time() + timeout
        with self._cv:
            while not self._closed and len(self._queue) == 0:
                remaining = end_time - time.time()
                if remaining <= 0:
                    return None
                self._cv.wait(timeout=remaining)

            if self._closed:
                return None

            if len(self._queue) == 0:
                return None

            return self._queue.popleft()

    def clear(self) -> int:
        """清空队列，返回清空数量。"""
        with self._cv:
            n = len(self._queue)
            self._queue.clear()
            return n

    def size(self) -> int:
        with self._cv:
            return len(self._queue)

    def close(self):
        """关闭队列并唤醒等待者。"""
        with self._cv:
            self._closed = True
            self._cv.notify_all()

