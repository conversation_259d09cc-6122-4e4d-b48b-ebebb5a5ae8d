import os
import logging
from datetime import datetime, timedelta
from app.utils.config import config
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

logger = logging.getLogger(__name__)

class TempCleaner:
    """临时文件清理工具"""
    
    def __init__(self):
        self.temp_dirs = [
            config.TEMP_VIDEO_DIR,
            config.TEMP_IMAGE_DIR,
            config.TEMP_REPORT_DIR
        ]
        self.retention_days = config.DATA_RETENTION_DAYS
        self.cleanup_time = config.DATA_CLEANUP_TIME
        self.scheduler = AsyncIOScheduler()
        
    def _parse_cleanup_time(self) -> tuple:
        """解析清理时间"""
        try:
            hour, minute = map(int, self.cleanup_time.split(':'))
            return hour, minute
        except:
            logger.error(f"清理时间格式错误: {self.cleanup_time}，使用默认值 00:00")
            return 0, 0
            
    def _is_expired_dir(self, dir_path: str) -> bool:
        """
        判断日期目录是否过期
        :param dir_path: 目录路径（格式：.../YYYYMMDD）
        :return: 是否过期
        """
        try:
            dir_name = os.path.basename(dir_path)
            dir_date = datetime.strptime(dir_name, '%Y%m%d')
            expire_date = datetime.now() - timedelta(days=self.retention_days)
            return dir_date.date() < expire_date.date()
        except (ValueError, IndexError) as e:
            logger.error(f"解析目录日期失败: {dir_path}, 错误: {str(e)}")
        return False

    def cleanup_dir(self, base_dir: str) -> int:
        """
        清理指定基础目录中的过期日期目录
        :param base_dir: 基础目录路径
        :return: 清理的文件数量
        """
        if not os.path.exists(base_dir):
            logger.warning(f"目录不存在: {base_dir}")
            return 0
            
        cleaned_count = 0
        try:
            # 遍历日期目录
            for date_dir in os.listdir(base_dir):
                dir_path = os.path.join(base_dir, date_dir)
                if not os.path.isdir(dir_path):
                    continue
                    
                # 检查日期目录是否过期
                if self._is_expired_dir(dir_path):
                    try:
                        # 统计文件数量
                        file_count = sum(len(files) for _, _, files in os.walk(dir_path))
                        # 删除整个日期目录
                        import shutil
                        shutil.rmtree(dir_path)
                        cleaned_count += file_count
                        logger.info(f"清理过期目录: {dir_path}, 文件数: {file_count}")
                    except Exception as e:
                        logger.error(f"删除目录失败: {dir_path}, 错误: {str(e)}")
                        
        except Exception as e:
            logger.error(f"清理目录失败: {base_dir}, 错误: {str(e)}")
            
        return cleaned_count
        
    def cleanup_all(self) -> dict:
        """
        清理所有临时目录
        :return: 每个目录清理的文件数量
        """
        results = {}
        for dir_path in self.temp_dirs:
            count = self.cleanup_dir(dir_path)
            results[dir_path] = count
            
        total = sum(results.values())
        logger.info(f"清理完成，共清理 {total} 个文件")
        return results
        
    def start_scheduled_cleanup(self):
        """启动定时清理任务"""
        hour, minute = self._parse_cleanup_time()
        
        # 添加定时任务
        self.scheduler.add_job(
            self.cleanup_all,
            CronTrigger(hour=hour, minute=minute),
            id='temp_cleanup',
            name='临时文件清理',
            replace_existing=True
        )
        
        # 启动调度器
        if not self.scheduler.running:
            self.scheduler.start()
            logger.info(f"定时清理任务已启动，将在每天 {hour:02d}:{minute:02d} 执行")
            
    def stop_scheduled_cleanup(self):
        """停止定时清理任务"""
        if self.scheduler.running:
            self.scheduler.shutdown()
            logger.info("定时清理任务已停止")

# 创建全局清理工具实例
temp_cleaner = TempCleaner() 