<!-- app/templates/video_index.html -->

<!DOCTYPE html>
<html>
<head>
    <title>视频识别 Demo</title>
    <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
    <h1>视频识别 Demo</h1>
    <a href="/">返回图像识别</a><br><br>
    
    <form id="video-upload-form" enctype="multipart/form-data">
        <label for="model-type">选择模型:</label>
        <select id="model-type" name="model_type">
            <option value="cv">CV模型（YOLO等）</option>
            <option value="multimodal">多模态模型（Ollama）</option>
        </select>
        <br><br>

        <label for="video-file">选择视频文件:</label>
        <input type="file" id="video-file" name="file" accept="video/*" required><br><br>
        
        <label for="prompt" id="prompt-label">目标/提示:</label>
        <input type="text" id="prompt" name="prompt" required><br><br>
        
        <label for="max-duration">分析时间范围（秒）:</label>
        <input type="number" id="max-duration" name="max_duration" min="1" placeholder="例如：60"><br><br>
        
        <button type="submit">上传并检测</button>
    </form>
    
    <div id="video-result"></div>
    <div id="annotated-frames">
        <h2>检测到目标的帧:</h2>
    </div>

    <script>
        const form = document.getElementById('video-upload-form');
        const modelSelect = document.getElementById('model-type');
        const promptLabel = document.getElementById('prompt-label');

        // 根据选择的模型类型更新提示文本
        modelSelect.addEventListener('change', () => {
            const isCV = modelSelect.value === 'cv';
            promptLabel.textContent = isCV ? '目标对象:' : '分析提示:';
        });

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData();
            formData.append('file', document.getElementById('video-file').files[0]);
            formData.append('prompt', document.getElementById('prompt').value);
            formData.append('model_type', modelSelect.value);
            
            const maxDuration = document.getElementById('max-duration').value;
            if (maxDuration) {
                formData.append('max_duration', maxDuration);
            }

            try {
                const response = await fetch('/api/video', {  // 更新API路径
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.error) {
                    document.getElementById('video-result').innerText = `错误: ${result.error}`;
                    return;
                }

                // 显示检测结果
                const resultDiv = document.getElementById('video-result');
                const framesDiv = document.getElementById('annotated-frames');
                framesDiv.innerHTML = '<h2>检测到目标的帧:</h2>';

                // 显示总体统计信息
                resultDiv.innerHTML = `
                    <h3>检测结果统计:</h3>
                    <p>总处理帧数: ${result.total_frames}</p>
                    <p>检测目标: ${result.target}</p>
                `;

                // 显示每个检测到目标的帧
                if (result.annotated_frames && result.annotated_frames.length > 0) {
                    result.annotated_frames.forEach(frame => {
                        const frameDiv = document.createElement('div');
                        frameDiv.className = 'frame-result';
                        
                        const img = document.createElement('img');
                        img.src = `data:image/jpeg;base64,${frame.image}`;
                        img.alt = `时间戳: ${frame.timestamp}秒`;
                        img.style.maxWidth = '300px';
                        img.style.margin = '10px';
                        
                        const timestamp = document.createElement('p');
                        timestamp.textContent = `时间戳: ${frame.timestamp.toFixed(2)}秒`;
                        
                        frameDiv.appendChild(img);
                        frameDiv.appendChild(timestamp);
                        framesDiv.appendChild(frameDiv);
                    });
                } else {
                    framesDiv.innerHTML += '<p>未检测到目标</p>';
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('video-result').innerText = '处理失败: ' + error.message;
            }
        });
    </script>
</body>
</html>
