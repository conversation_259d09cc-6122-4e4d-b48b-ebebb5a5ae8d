import asyncio
import aiohttp
import logging
import json
import time
import random
from datetime import datetime
from typing import Optional, Dict, Any, Callable
from app.utils.config import config
from app.utils.shared_state import active_drones
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

logger = logging.getLogger(__name__)

class WebSocketClient:
    """WebSocket客户端，用于处理无人机数据流"""
    
    def __init__(self):
        """初始化WebSocket客户端"""
        self.token: Optional[str] = None
        self.workspace_id: Optional[str] = None
        self.ws: Optional[aiohttp.ClientWebSocketResponse] = None
        self.session: Optional[aiohttp.ClientSession] = None
        self.message_handlers: Dict[str, Callable] = {}
        self._running = False
        self.scheduler = AsyncIOScheduler()
        
        # 重连相关状态 - 使用配置参数
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = config.WEBSOCKET_MAX_RECONNECT_ATTEMPTS
        self.base_reconnect_delay = config.WEBSOCKET_BASE_RECONNECT_DELAY
        self.max_reconnect_delay = config.WEBSOCKET_MAX_RECONNECT_DELAY
        self.last_reconnect_time = 0
        self.last_message_time = time.time()
        self.connection_healthy = True
        
        # 智能休眠模式 - 使用配置参数
        self.night_mode_enabled = False
        self.night_mode_start_hour = config.WEBSOCKET_NIGHT_MODE_START_HOUR
        self.night_mode_end_hour = config.WEBSOCKET_NIGHT_MODE_END_HOUR
        self.night_mode_reconnect_interval = config.WEBSOCKET_NIGHT_MODE_RECONNECT_INTERVAL
        
        # 连接健康检查 - 使用配置参数
        self.health_check_interval = config.WEBSOCKET_HEALTH_CHECK_INTERVAL
        self.last_health_check = time.time()
        self.consecutive_failures = 0
        self.max_consecutive_failures = config.WEBSOCKET_MAX_CONSECUTIVE_FAILURES
        self.no_message_timeout = config.WEBSOCKET_NO_MESSAGE_TIMEOUT
        self.reconnect_cooldown = config.WEBSOCKET_RECONNECT_COOLDOWN
        self.last_successful_connect = 0
        
        # 添加默认的无人机状态处理器
        self.register_message_handler('drone_status', self._process_drone_status)

    def _is_night_mode(self) -> bool:
        """判断是否处于夜间模式"""
        current_hour = datetime.now().hour
        return current_hour >= self.night_mode_start_hour or current_hour < self.night_mode_end_hour

    def _calculate_reconnect_delay(self) -> float:
        """计算重连延迟时间（指数退避）"""
        if self.reconnect_attempts == 0:
            return self.base_reconnect_delay
        
        # 指数退避算法
        delay = min(
            self.base_reconnect_delay * (2 ** (self.reconnect_attempts - 1)),  # 修正指数计算
            self.max_reconnect_delay
        )
        
        # 添加随机抖动，避免多个客户端同时重连
        jitter = random.uniform(0.8, 1.2)
        return delay * jitter

    def _should_attempt_reconnect(self) -> bool:
        """判断是否应该尝试重连"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.warning(f"已达到最大重连次数 {self.max_reconnect_attempts}，停止重连")
            return False
        
        # 检查重连间隔
        current_time = time.time()
        if current_time - self.last_reconnect_time < self._calculate_reconnect_delay():
            return False
        
        return True

    def _reset_reconnect_state(self):
        """重置重连状态"""
        self.reconnect_attempts = 0
        self.consecutive_failures = 0
        self.connection_healthy = True
        self.last_successful_connect = time.time()
        logger.info("重置WebSocket重连状态")

    def _is_connection_healthy(self) -> bool:
        """检查连接是否健康（基于消息时间）"""
        try:
            if not self.ws or self.ws.closed:
                return False

            # 检查最后消息时间
            current_time = time.time()
            if current_time - self.last_message_time > self.no_message_timeout:
                logger.warning(f"WebSocket连接长时间无消息({self.no_message_timeout}秒)，可能已断开")
                return False

            return True

        except Exception as e:
            logger.error(f"连接健康检查失败: {str(e)}")
            return False

    async def get_token(self) -> bool:
        """
        获取认证token
        :return: 是否成功获取token
        """
        try:
            base_url = config.WEBSOCKET_URL.rstrip('/')  # 移除末尾的斜杠
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{base_url}/manage/api/v1/login",
                    json={
                        "username": config.WEBSOCKET_USERNAME,
                        "password": config.WEBSOCKET_PASSWORD,
                        "flag": config.WEBSOCKET_FLAG
                    }
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'data' in data:
                            self.token = data['data']['access_token']
                            self.workspace_id = data['data']['workspace_id']
                            logger.info("成功获取新token")
                            return True
                    logger.error(f"获取token失败: {response.status} - {await response.text()}")
                    return False
        except Exception as e:
            logger.error(f"获取token异常: {str(e)}")
            return False

    def get_websocket_url(self) -> str:
        """
        获取WebSocket URL
        :return: 完整的WebSocket URL
        """
        if not self.token:
            raise ValueError("Token未初始化")
            
        # 从 base_url 中提取主机和端口
        base_url = config.WEBSOCKET_URL.rstrip('/')  # 移除末尾的斜杠
        # 替换 http:// 为 ws://
        ws_url = base_url.replace('http://', 'ws://')
        # 添加 WebSocket 路径和 token
        return f"{ws_url}/api/v1/ws?x-auth-token={self.token}"

    async def validate_token(self) -> bool:
        """
        验证当前token是否有效
        :return: token是否有效
        """
        if not self.token:
            return False
            
        try:
            base_url = config.WEBSOCKET_URL.rstrip('/')  # 移除末尾的斜杠
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{base_url}/api/v1/validate_token",
                    headers={
                        "Authorization": f"Bearer {self.token}",
                        "workspace-id": self.workspace_id
                    }
                ) as response:
                    if response.status == 200:
                        return True
                    elif response.status == 401:
                        logger.warning("Token已过期，需要重新获取")
                        self.token = None
                        return False
                    else:
                        logger.error(f"验证token失败: {response.status} - {await response.text()}")
                        return False
        except Exception as e:
            logger.error(f"验证token时发生异常: {str(e)}")
            return False

    async def connect(self) -> bool:
        """
        建立WebSocket连接
        :return: 是否成功建立连接
        """
        try:
            # 检查重连冷却时间
            current_time = time.time()
            if current_time - self.last_successful_connect < self.reconnect_cooldown:
                logger.debug(f"重连冷却中，剩余时间: {self.reconnect_cooldown - (current_time - self.last_successful_connect):.1f}秒")
                return False

            # 如果没有token或token无效，重新获取
            if not self.token or not await self.validate_token():
                if not await self.get_token():
                    return False

            # 获取WebSocket URL
            ws_url = self.get_websocket_url()

            # 安全关闭现有连接
            await self._safe_close_connection()

            # 创建新的会话，增加超时设置
            timeout = aiohttp.ClientTimeout(total=self.base_reconnect_delay * 2)
            self.session = aiohttp.ClientSession(timeout=timeout)

            # 建立WebSocket连接，使用token进行认证
            logger.info(f"尝试连接WebSocket: {ws_url}")
            self.ws = await self.session.ws_connect(
                ws_url,
                headers={
                    "Authorization": f"Bearer {self.token}",
                    "workspace-id": self.workspace_id
                },
                heartbeat=None  # 禁用aiohttp内置心跳
            )

            # 连接成功后重置重连状态
            self._reset_reconnect_state()
            logger.info("WebSocket连接成功建立")
            return True

        except aiohttp.ClientError as e:
            logger.error(f"WebSocket连接失败: {str(e)}")
            if "401" in str(e) or "Unauthorized" in str(e):
                logger.info("认证失败，重新获取token")
                self.token = None
            return False
        except Exception as e:
            logger.error(f"建立WebSocket连接时发生异常: {str(e)}")
            return False

    async def _safe_close_connection(self):
        """安全关闭现有连接"""
        try:
            if self.ws and not self.ws.closed:
                await self.ws.close()
                self.ws = None
        except Exception as e:
            logger.debug(f"关闭WebSocket连接时出现异常: {str(e)}")

        try:
            if self.session and not self.session.closed:
                await self.session.close()
                self.session = None
        except Exception as e:
            logger.debug(f"关闭会话时出现异常: {str(e)}")

    async def reconnect(self) -> bool:
        """
        重新连接WebSocket
        :return: 是否成功重连
        """
        try:
            if self.ws:
                await self.ws.close()
            if self.session:
                await self.session.close()
            
            return await self.connect()
            
        except Exception as e:
            logger.error(f"WebSocket重连失败: {str(e)}")
            return False

    async def refresh_token(self) -> None:
        """刷新token"""
        try:
            logger.info("开始刷新token")
            if await self.get_token():
                logger.info("token刷新成功")
            else:
                logger.error("token刷新失败")
        except Exception as e:
            logger.error(f"刷新token时发生异常: {str(e)}")

    def register_message_handler(self, name: str, handler: Callable) -> None:
        """
        注册消息处理器
        :param name: 处理器名称
        :param handler: 处理器函数
        """
        self.message_handlers[name] = handler
        logger.info(f"注册消息处理器: {name}")

    def unregister_message_handler(self, name: str) -> None:
        """
        注销消息处理器
        :param name: 处理器名称
        """
        if name in self.message_handlers:
            del self.message_handlers[name]
            logger.info(f"注销消息处理器: {name}")

    async def _handle_message(self, message: str) -> None:
        """
        处理接收到的WebSocket消息
        :param message: 消息内容
        """
        t0 = time.monotonic()
        # logger.debug("[诊断] 进入 _handle_message")
        try:
            # 更新最后消息时间
            self.last_message_time = time.time()

            data = json.loads(message)
            # logger.debug(f"WebSocket接收到消息，类型: {data.get('type', 'unknown')}, biz_code: {data.get('biz_code', 'unknown')}")

            # 处理不同类型的消息
            if data.get("type") == "ping":
                await self._send_pong()
            else:
                # 处理无人机状态数据
                if data.get("biz_code") == "dock_osd":
                    t_proc0 = time.monotonic()
                    await self._process_drone_status(data)
                    # logger.debug(f"[诊断] _process_drone_status 耗时: {(time.monotonic()-t_proc0)*1000:.1f} ms")

                # 调用注册的消息处理器
                for handler in self.message_handlers.values():
                    t_h0 = time.monotonic()
                    await handler(data)
                    # logger.debug(f"[诊断] handler耗时: {(time.monotonic()-t_h0)*1000:.1f} ms")

        except json.JSONDecodeError:
            logger.error(f"解析消息失败: {message}")
        except Exception as e:
            logger.error(f"处理消息时发生异常: {str(e)}")
        # finally:
        #     logger.debug(f"[诊断] 退出 _handle_message, 耗时={(time.monotonic()-t0)*1000:.1f} ms")

    async def _process_drone_status(self, data: Dict[str, Any]) -> None:
        """
        处理无人机状态数据
        :param data: 无人机状态数据
        """
        try:
            if 'data' not in data or 'data' not in data['data']:
                return
                
            host_data = data['data']['data'].get('host', {})
            sn = data['data']['data'].get('sn')
            
            if not sn:
                return
                
            # 获取mode_code
            mode_code = host_data.get('mode_code')
            logger.info(f"从WebSocket消息提取mode_code: {mode_code}, sn: {sn}")
            if mode_code is None:
                logger.info(f"无人机 {sn} 的mode_code为空，跳过处理")
                return

            # 获取device_sn
            sub_device = host_data.get('sub_device', {})
            device_sn = sub_device.get('device_sn')
            logger.info(f"从WebSocket消息提取device_sn: {device_sn}")

            if not device_sn:
                logger.info(f"无人机 {sn} 的device_sn为空，跳过处理")
                return

            # 判断无人机是否活跃
            is_active = mode_code in config.WEBSOCKET_ACTIVE_MODE_CODES
            logger.info(f"无人机活跃状态判断: device_sn={device_sn}, mode_code={mode_code}, is_active={is_active}, 活跃模式列表={config.WEBSOCKET_ACTIVE_MODE_CODES}")
            
            # 更新无人机状态 - 使用共享状态管理器
            from app.utils.shared_state import shared_state_manager
            status = {
                'device_sn': device_sn,
                'mode_code': mode_code,
                'is_active': is_active,
                'last_update': time.time(),
                'sn': sn
            }
            shared_state_manager.update_drone_status(device_sn, status)
            logger.info(f"WebSocket已更新无人机状态到共享状态管理器: device_sn={device_sn}, mode_code={mode_code}, is_active={is_active}, sn={sn}")

            logger.debug(f"更新无人机状态: {device_sn}, mode_code: {mode_code}, is_active: {is_active}")
            
        except Exception as e:
            logger.error(f"处理无人机状态数据异常: {str(e)}")

    def get_active_drone_ids(self) -> list:
        """
        获取当前活跃的无人机ID列表（使用统一状态管理）
        :return: 活跃无人机ID列表
        """
        from app.utils.shared_state import shared_state_manager
        active_ids = shared_state_manager.get_active_drone_ids()
        logger.info(f"WebSocketClient获取活跃无人机ID列表: {active_ids}")
        return active_ids

    def is_drone_active(self, device_sn: str) -> bool:
        """
        检查指定无人机是否活跃（使用统一状态管理）
        :param device_sn: 无人机ID
        :return: 是否活跃
        """
        from app.utils.shared_state import shared_state_manager
        is_active = shared_state_manager.is_drone_active(device_sn)
        logger.info(f"WebSocketClient检查无人机活跃状态: device_sn={device_sn}, is_active={is_active}")
        return is_active

    async def _send_pong(self) -> None:
        """发送pong消息（响应服务器ping）"""
        try:
            if self.ws and not self.ws.closed:
                await self.ws.send_str("pong")
                logger.debug("发送pong响应")
        except Exception as e:
            logger.error(f"发送pong消息失败: {str(e)}")

    async def _process_messages(self):
        """处理WebSocket消息的内部方法（简化版）"""
        # 仅在连接建立与结束时记录关键日志，避免高频日志
        try:
            logger.info("开始WebSocket消息循环")
            async for msg in self.ws:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    await self._handle_message(msg.data)
                elif msg.type == aiohttp.WSMsgType.CLOSED:
                    logger.info("WebSocket连接已关闭")
                    break
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    logger.error(f"WebSocket错误: {msg.data}")
                    break

                # 删除周期性心跳日志，避免高频日志干扰排查

        except Exception as e:
            logger.error(f"WebSocket消息循环异常: {str(e)}")
        finally:
            logger.info("WebSocket消息循环结束")

    def setup_scheduler(self) -> None:
        """设置定时任务"""
        if not self.scheduler.running:
            # 设置token刷新任务
            hour = config.WEBSOCKET_TOKEN_REFRESH_HOUR
            minute = config.WEBSOCKET_TOKEN_REFRESH_MINUTE
            self.scheduler.add_job(
                self.refresh_token,
                CronTrigger(hour=hour, minute=minute),  # 每天指定时间刷新
                id='token_refresh'
            )
            self.scheduler.start()
            logger.info(f"已设置token刷新任务，刷新时间: {hour:02d}:{minute:02d}")

    async def start(self) -> None:
        """启动WebSocket客户端"""
        if self._running:
            return
            
        self._running = True
        self.setup_scheduler()
        
        while self._running:
            try:
                # 检查是否应该尝试重连
                if not self.ws and not self._should_attempt_reconnect():
                    # 夜间模式下的特殊处理
                    if self._is_night_mode():
                        await asyncio.sleep(self.night_mode_reconnect_interval)
                    else:
                        await asyncio.sleep(10)  # 增加等待时间
                    continue

                # 尝试建立连接
                if not self.ws:
                    if await self.connect():
                        logger.info("WebSocket连接建立成功，开始消息处理")
                    else:
                        self.reconnect_attempts += 1
                        self.consecutive_failures += 1
                        self.last_reconnect_time = time.time()

                        delay = self._calculate_reconnect_delay()
                        logger.warning(f"WebSocket连接失败，第 {self.reconnect_attempts} 次重连，延迟 {delay:.1f} 秒")
                        await asyncio.sleep(delay)
                        continue

                # 连接成功，处理消息
                try:
                    await self._process_messages()
                except Exception as e:
                    logger.error(f"WebSocket消息处理异常: {str(e)}")
                finally:
                    # 确保连接被正确关闭
                    await self._safe_close_connection()

                # 连接断开后的处理
                if self._running:
                    self.consecutive_failures += 1
                    logger.warning(f"WebSocket连接断开，连续失败次数: {self.consecutive_failures}")

                    # 检查是否需要重置token（减少重置频率）
                    if self.consecutive_failures >= self.max_consecutive_failures:
                        # 检查连接时长，如果连接时间很短可能是配置问题
                        connection_duration = time.time() - self.last_successful_connect
                        if connection_duration < 60:  # 连接不到1分钟就断开
                            logger.warning("连接时间过短，可能是认证问题，重置token")
                            self.token = None
                        else:
                            logger.warning("连接时间较长后断开，可能是网络问题，不重置token")
                        self.consecutive_failures = 0

                    # 夜间模式下的特殊处理
                    if self._is_night_mode():
                        await asyncio.sleep(self.night_mode_reconnect_interval)
                    else:
                        await asyncio.sleep(10)  # 增加等待时间

            except Exception as e:
                logger.error(f"WebSocket处理异常: {str(e)}")
                self.consecutive_failures += 1
                await asyncio.sleep(10)  # 增加等待时间

    async def stop(self) -> None:
        """停止WebSocket客户端"""
        if not self._running:
            return
            
        self._running = False
        logger.info("停止WebSocket客户端")
        
        try:
            # 停止调度器
            if self.scheduler.running:
                self.scheduler.shutdown()
                logger.info("WebSocket调度器已停止")
            
            # 安全关闭连接
            await self._safe_close_connection()
            logger.info("WebSocket连接和会话已关闭")
                    
            logger.info("WebSocket客户端已完全停止")
            
        except Exception as e:
            logger.error(f"停止WebSocket客户端时发生错误: {str(e)}")
            import traceback
            logger.error(f"停止WebSocket客户端错误详情: {traceback.format_exc()}")
        finally:
            # 确保状态重置
            self._running = False 